/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include <cassert>
#include <cstring>
#include <memory>
#include <string>
#include <vector>
#include "defs.h"
#include "record/rm_defs.h"
#include "system/sm_meta.h" // Added for ColMeta definition

// Forward declarations
namespace ast {
    struct Expr;
}
class Query;
class Plan;

struct TabCol {
    std::string tab_name;
    std::string col_name;
    bool is_vir = false;
    std::shared_ptr<ast::Expr> expr;

    friend bool operator<(const TabCol &x, const TabCol &y) {
        return std::make_pair(x.tab_name, x.col_name) < std::make_pair(y.tab_name, y.col_name);
    }
    
    friend bool operator==(const TabCol &x, const TabCol &y) {
        return x.tab_name == y.tab_name && x.col_name == y.col_name;
    }
    
    friend bool operator!=(const TabCol &x, const TabCol &y) {
        return !(x == y);
    }
};

struct Value {
    ColType type;  // type of value
    union {
        int int_val;      // int value
        float float_val;  // float value
    };
    std::string str_val;  // string value
    std::string raw_literal_text; // Stores the original raw text of the literal, if applicable

    std::shared_ptr<RmRecord> raw;  // raw record buffer

    void set_int(int int_val_) {
        type = TYPE_INT;
        int_val = int_val_;
    }

    void set_float(double float_val_) {
        type = TYPE_FLOAT;
        float_val = float_val_;
    }

    void set_float(float float_val_) {
        type = TYPE_FLOAT;
        float_val = float_val_;
    }

    void set_float(int int_val_) {
        type = TYPE_FLOAT;
        float_val = (float) int_val_;
    }


    void set_str(std::string str_val_) {
        type = TYPE_STRING;
        str_val = std::move(str_val_);
    }

    void init_raw(int len) {
        if (raw != nullptr) {
            return;  // 如果已经初始化过，直接返回
        }
        raw = std::make_shared<RmRecord>(len);
        if (type == TYPE_INT) {
            assert(len == sizeof(int));
            *(int *)(raw->data) = int_val;
        } else if (type == TYPE_FLOAT) {
            assert(len == sizeof(float));
            *(float *)(raw->data) = float_val;
        } else if (type == TYPE_STRING) {
            if (len < (int)str_val.size()) {
                throw StringOverflowError();
            }
            memset(raw->data, 0, len);
            memcpy(raw->data, str_val.c_str(), str_val.size());
        }
    }
    
    // 重载value的比较运算符 > < == != >= <=
    bool operator==(const Value &rhs) const {
        if (!checkColType(type, rhs.type)) {
            return 0; // false
        }
        // check if the data is equal using conditional statements
        if (type == TYPE_INT) {
            if (rhs.type == TYPE_INT) {
                return int_val == rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return int_val == rhs.float_val;
            }
        } else if (type == TYPE_STRING) {
            return strcmp(str_val.c_str(), rhs.str_val.c_str()) == 0; // gcc helper
        } else if (type == TYPE_FLOAT) {
            if (rhs.type == TYPE_INT) {
                return float_val == rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return float_val == rhs.float_val;
            }
        }
        throw IncompatibleTypeError(coltype2str(type), coltype2str(rhs.type)); // else is not same type
    }

    bool operator>(const Value &rhs) const {
        if (!checkColType(type, rhs.type)) {
            throw IncompatibleTypeError(coltype2str(type), coltype2str(rhs.type)); // is not >
        }
        // greater than comparison using conditional logic
        if (type == TYPE_INT) {
            if (rhs.type == TYPE_INT) {
                return int_val > rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return int_val > rhs.float_val;
            }
        } else if (type == TYPE_FLOAT) {
            if (rhs.type == TYPE_INT) {
                return float_val > rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return float_val > rhs.float_val;
            }
        } else if (type == TYPE_STRING) {
            return strcmp(str_val.c_str(), rhs.str_val.c_str()) > 0;
        }
        throw IncompatibleTypeError(coltype2str(type), coltype2str(rhs.type));
    }

    bool operator<(const Value &rhs) const {
        if (!checkColType(type, rhs.type)) {
            throw IncompatibleTypeError(coltype2str(type), coltype2str(rhs.type));
        }
        // less than comparison using conditional branches
        if (type == TYPE_INT) {
            if (rhs.type == TYPE_INT) {
                return int_val < rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return int_val < rhs.float_val;
            }
        } else if (type == TYPE_FLOAT) {
            if (rhs.type == TYPE_INT) {
                return float_val < rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return float_val < rhs.float_val;
            }
        } else if (type == TYPE_STRING) {
            return strcmp(str_val.c_str(), rhs.str_val.c_str()) < 0;
        }
        throw IncompatibleTypeError(coltype2str(type), coltype2str(rhs.type));
    }

    bool operator!=(const Value &rhs) const {
        return !(*this == rhs);
    }

    bool operator>=(const Value &rhs) const {
        if (!checkColType(type, rhs.type)) {
            throw IncompatibleTypeError(coltype2str(type), coltype2str(rhs.type));
        }
        // greater than or equal comparison using if-else structure
        if (type == TYPE_INT) {
            if (rhs.type == TYPE_INT) {
                return int_val >= rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return int_val >= rhs.float_val;
            }
        } else if (type == TYPE_FLOAT) {
            if (rhs.type == TYPE_INT) {
                return float_val >= rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return float_val >= rhs.float_val;
            }
        } else if (type == TYPE_STRING) {
            return strcmp(str_val.c_str(), rhs.str_val.c_str()) >= 0;
        }
        throw IncompatibleTypeError(coltype2str(type), coltype2str(rhs.type));
    }

    bool operator<=(const Value &rhs) const {
        if (!checkColType(type, rhs.type)) {
            throw IncompatibleTypeError(coltype2str(type), coltype2str(rhs.type));
        }
        // less than or equal comparison using conditional statements
        if (type == TYPE_INT) {
            if (rhs.type == TYPE_INT) {
                return int_val <= rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return int_val <= rhs.float_val;
            }
        } else if (type == TYPE_FLOAT) {
            if (rhs.type == TYPE_INT) {
                return float_val <= rhs.int_val;
            } else if (rhs.type == TYPE_FLOAT) {
                return float_val <= rhs.float_val;
            }
        } else if (type == TYPE_STRING) {
            return strcmp(str_val.c_str(), rhs.str_val.c_str()) <= 0;
        }
        throw IncompatibleTypeError(coltype2str(type), coltype2str(rhs.type));
    }
};

enum CompOp { OP_EQ, OP_NE, OP_LT, OP_GT, OP_LE, OP_GE ,OP_IN };

enum AggregationOp {
    AG_COUNT, AG_SUM, AG_MAX, AG_MIN, AG_AVG
};

struct Aggregation {
    AggregationOp op;
    TabCol target_col;
};


struct Condition {
    TabCol lhs_col;   // left-hand side column
    CompOp op;        // comparison operator
    bool is_rhs_val;  // true if right-hand side is a value (not a column)
    TabCol rhs_col;   // right-hand side column
    Value rhs_val;    // right-hand side value

    std::vector<Value> x_rhs_vals;
    std::shared_ptr<ast::Expr> x_rhs_expr;
    std::shared_ptr<Query> x_subquery_res;

    // Added iterators for direct ColMeta access
    std::vector<ColMeta>::iterator lhs;
    std::vector<ColMeta>::iterator rhs; // Only valid if is_rhs_val is false
    
    friend bool operator==(const Condition &x, const Condition &y) {
        return x.lhs_col == y.lhs_col &&
               x.op == y.op &&
               x.is_rhs_val == y.is_rhs_val &&
               (!x.is_rhs_val ? (x.rhs_col == y.rhs_col) : (x.rhs_val == y.rhs_val));
    }
    
    friend bool operator!=(const Condition &x, const Condition &y) {
        return !(x == y);
    }
};

struct SetClause {
    TabCol lhs;
    Value rhs;
    std::shared_ptr<ast::Expr> expr;  // 添加表达式支持，在执行阶段计算
};