/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "defs.h"
#include "storage/buffer_pool_manager.h"

constexpr int RM_NO_PAGE = -1;
constexpr int RM_FILE_HDR_PAGE = 0;
constexpr int RM_FIRST_RECORD_PAGE = 1;
constexpr int RM_MAX_RECORD_SIZE = 512;

// MVCC相关常量定义
constexpr int64_t INVALID_UNDO_PTR = -1;  // 无效的撤销日志指针

// 撤销日志指针类型，指向撤销日志的位置
using undo_ptr_t = int64_t;

/**
 * @brief 撤销日志操作类型
 */
enum class UndoLogType {
    UPDATE = 0,  // 更新操作的撤销日志
    DELETE = 1   // 删除操作的撤销日志
};

/**
 * @brief 撤销日志头部信息
 */
struct UndoLogHeader {
    UndoLogType log_type_;        // 撤销日志类型
    txn_id_t creator_txn_id_;     // 创建此撤销日志的事务ID
    undo_ptr_t prev_undo_ptr_;    // 指向更早版本的撤销日志指针
    int record_size_;             // 记录数据的大小
    
    UndoLogHeader() = default;
    
    UndoLogHeader(UndoLogType log_type, txn_id_t creator_txn_id, 
                  undo_ptr_t prev_undo_ptr, int record_size)
        : log_type_(log_type), creator_txn_id_(creator_txn_id),
          prev_undo_ptr_(prev_undo_ptr), record_size_(record_size) {}
};

/**
 * @brief 撤销日志记录，包含头部信息和数据
 */
struct UndoLogRecord {
    UndoLogHeader header_;
    char* data_;  // 旧版本的记录数据
    bool allocated_ = false;
    
    UndoLogRecord() = default;
    
    UndoLogRecord(UndoLogType log_type, txn_id_t creator_txn_id,
                  undo_ptr_t prev_undo_ptr, int record_size, const char* data) 
        : header_(log_type, creator_txn_id, prev_undo_ptr, record_size) {
        if (record_size > 0 && data != nullptr) {
            data_ = new char[record_size];
            memcpy(data_, data, record_size);
            allocated_ = true;
        } else {
            data_ = nullptr;
        }
    }
    
    UndoLogRecord(const UndoLogRecord& other) {
        header_ = other.header_;
        if (other.allocated_ && other.data_ != nullptr) {
            data_ = new char[header_.record_size_];
            memcpy(data_, other.data_, header_.record_size_);
            allocated_ = true;
        } else {
            data_ = nullptr;
            allocated_ = false;
        }
    }
    
    UndoLogRecord& operator=(const UndoLogRecord& other) {
        if (this != &other) {
            if (allocated_ && data_ != nullptr) {
                delete[] data_;
            }
            
            header_ = other.header_;
            if (other.allocated_ && other.data_ != nullptr) {
                data_ = new char[header_.record_size_];
                memcpy(data_, other.data_, header_.record_size_);
                allocated_ = true;
            } else {
                data_ = nullptr;
                allocated_ = false;
            }
        }
        return *this;
    }
    
    ~UndoLogRecord() {
        if (allocated_ && data_ != nullptr) {
            delete[] data_;
        }
        data_ = nullptr;
        allocated_ = false;
    }
};

struct TupleMeta {
    timestamp_t ts_;
    bool is_deleted_;
    // MVCC新增字段
    txn_id_t creator_txn_id_;     // 创建此版本记录的事务ID
    undo_ptr_t prev_version_ptr_; // 指向前一个版本的撤销日志指针

    TupleMeta() : ts_(0), is_deleted_(false), 
                  creator_txn_id_(INVALID_TXN_ID), 
                  prev_version_ptr_(INVALID_UNDO_PTR) {}

    TupleMeta(timestamp_t ts, bool is_deleted, txn_id_t creator_txn_id, 
              undo_ptr_t prev_version_ptr = INVALID_UNDO_PTR)
        : ts_(ts), is_deleted_(is_deleted), 
          creator_txn_id_(creator_txn_id), 
          prev_version_ptr_(prev_version_ptr) {}

    friend auto operator==(const TupleMeta &a, const TupleMeta &b) {
        return a.ts_ == b.ts_ && a.is_deleted_ == b.is_deleted_ &&
               a.creator_txn_id_ == b.creator_txn_id_ && 
               a.prev_version_ptr_ == b.prev_version_ptr_;
    }

    friend auto operator!=(const TupleMeta &a, const TupleMeta &b) { return !(a == b); }
};

/* 文件头，记录表数据文件的元信息，写入磁盘中文件的第0号页面 */
struct RmFileHdr {
    int record_size;            // 表中每条记录的实际存储大小（包含TupleMeta + 用户数据）
    int user_record_size;       // 用户数据部分的大小（不包含TupleMeta）
    int num_pages;              // 文件中分配的页面个数（初始化为1）
    int num_records_per_page;   // 每个页面最多能存储的元组个数
    int first_free_page_no;     // 文件中当前第一个包含空闲空间的页面号（初始化为-1）
    int bitmap_size;            // 每个页面bitmap大小
};

/* 表数据文件中每个页面的页头，记录每个页面的元信息 */
struct RmPageHdr {
    int next_free_page_no;  // 当前页面满了之后，下一个包含空闲空间的页面号（初始化为-1）
    int num_records;        // 当前页面中当前已经存储的记录个数（初始化为0）
};

/* 表中的记录 */
struct RmRecord {
    char* data;  // 记录的数据
    int size;    // 记录的大小
    bool allocated_ = false;    // 是否已经为数据分配空间

    RmRecord() = default;

    RmRecord(const RmRecord& other) {
        size = other.size;
        data = new char[size];
        memcpy(data, other.data, size);
        allocated_ = true;
    };


    RmRecord &operator=(const RmRecord& other) {
        size = other.size;
        data = new char[size];
        memcpy(data, other.data, size);
        allocated_ = true;
        return *this;
    };

    RmRecord(int size_) {
        size = size_;
        data = new char[size_];
        allocated_ = true;
    }

    RmRecord(int size_, char* data_) {
        size = size_;
        data = new char[size_];
        memcpy(data, data_, size_);
        allocated_ = true;
    }

    void SetData(char* data_) {
        memcpy(data, data_, size);
    }

    void Deserialize(const char* data_) {
        size = *reinterpret_cast<const int*>(data_);
        if(allocated_) {
            delete[] data;
        }
        data = new char[size];
        memcpy(data, data_ + sizeof(int), size);
    }

    ~RmRecord() {
        if(allocated_) {
            delete[] data;
        }
        allocated_ = false;
        data = nullptr;
    }
};
