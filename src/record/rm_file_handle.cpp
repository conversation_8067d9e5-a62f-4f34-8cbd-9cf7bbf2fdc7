/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL
v2. You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "rm_file_handle.h"
#include "transaction/undo_log_manager.h"

/**
 * @description: 获取当前表中记录号为rid的记录
 * @param {Rid&} rid 记录号，指定记录的位置
 * @param {Context*} context
 * @return {unique_ptr<RmRecord>} rid对应的记录对象指针
 */
std::unique_ptr<RmRecord> RmFileHandle::get_record(const Rid &rid,
                                                   Context *context) const {
  // 1. 获取指定记录所在的page handle
  auto &&page_handle = fetch_page_handle(rid.page_no);

  // 2. 检查记录是否存在
  if (!Bitmap::is_set(page_handle.bitmap, rid.slot_no)) {
    // Record not found in bitmap, unpin the page and return nullptr
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), false);
    throw RecordNotFoundError(rid.page_no, rid.slot_no);
  }

  // 3. MVCC: 获取完整记录（包含TupleMeta + 用户数据）
  // 创建RmRecord，包含完整的存储格式
  auto &&record = std::make_unique<RmRecord>(file_hdr_.record_size, page_handle.get_slot(rid.slot_no));
  buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), false);
  return std::move(record);
}

/**
 * @description: 在当前表中插入一条记录，不指定插入位置
 * @param {char*} buf 要插入的记录的数据
 * @param {Context*} context
 * @return {Rid} 插入的记录的记录号（位置）
 */
Rid RmFileHandle::insert_record(char *buf, Context *context) {
    // 1. 获取当前未满的page handle
    RmPageHandle page_handle = create_page_handle();
    
    // 2. 在page handle中找到空闲slot位置
    int slot_no = Bitmap::first_bit(0, page_handle.bitmap, file_hdr_.num_records_per_page);
    
    // 3. MVCC: 创建包含TupleMeta的完整记录
    TupleMeta meta;
    if (context && context->txn_) {
        // 初始化TupleMeta
        // 注意：插入时使用一个临时时间戳，在可见性检查时会动态获取正确的时间戳
        meta.ts_ = context->txn_->get_read_ts();
        meta.is_deleted_ = false;
        meta.creator_txn_id_ = context->txn_->get_transaction_id();
        meta.prev_version_ptr_ = INVALID_UNDO_PTR;
    }
    
    // 创建完整记录（TupleMeta + 用户数据）
    auto full_record = create_full_record(buf, meta);
    
    // 将完整记录复制到slot位置
    char* slot = page_handle.get_slot(slot_no);
    memcpy(slot, full_record->data, file_hdr_.record_size);
    
    // 4. 更新page_handle.page_hdr中的数据结构
    // 将bitmap中对应位置设为1，表示该slot已被使用
    Bitmap::set(page_handle.bitmap, slot_no);
    page_handle.page_hdr->num_records++;
    
    // 检查页面是否已满，更新first_free_page_no
    if (page_handle.page_hdr->num_records == file_hdr_.num_records_per_page) {
        // 页面已满，需要更新file_hdr_.first_free_page_no
        file_hdr_.first_free_page_no = page_handle.page_hdr->next_free_page_no;
        page_handle.page_hdr->next_free_page_no = RM_NO_PAGE;
        // Persist the updated file header immediately
        disk_manager_->write_page(fd_, RM_FILE_HDR_PAGE, (char*)&file_hdr_, sizeof(file_hdr_));
        if (fsync(fd_) == -1) {
            // Optional: Handle fsync error, though it's often ignored or hard to recover from
            // For robustness, one might throw an exception or log it.
            // throw UnixError("fsync failed after updating file header in insert_record");
        }
    }
    
    // 标记页面为脏页
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
    
    // 返回记录的Rid
    return Rid{page_handle.page->get_page_id().page_no, slot_no};
}

/**
 * @description: 在当前表中的指定位置插入一条记录
 * @param {Rid&} rid 要插入记录的位置
 * @param {char*} buf 要插入记录的数据
 */
void RmFileHandle::insert_record(const Rid &rid, char *buf) {
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    
    // MVCC: 创建默认的TupleMeta（这个方法主要用于内部操作）
    TupleMeta meta;
    // 对于指定位置插入，通常是内部操作，使用默认值
    meta.ts_ = 0;
    meta.is_deleted_ = false;
    meta.creator_txn_id_ = INVALID_TXN_ID;
    meta.prev_version_ptr_ = INVALID_UNDO_PTR;
    
    // 创建完整记录（TupleMeta + 用户数据）
    auto full_record = create_full_record(buf, meta);
    
    // 检查指定位置是否已有记录
    if (Bitmap::is_set(page_handle.bitmap, rid.slot_no)) {
        // 如果已经有记录，直接覆盖
        char* slot = page_handle.get_slot(rid.slot_no);
        memcpy(slot, full_record->data, file_hdr_.record_size);
    } else {
        // 如果没有记录，需要设置bitmap并增加记录数
        char* slot = page_handle.get_slot(rid.slot_no);
        memcpy(slot, full_record->data, file_hdr_.record_size);
        Bitmap::set(page_handle.bitmap, rid.slot_no);
        page_handle.page_hdr->num_records++;
        
        // 检查页面是否已满
        if (page_handle.page_hdr->num_records == file_hdr_.num_records_per_page) {
            // 页面已满，需要从空闲页面链表中移除
            if (file_hdr_.first_free_page_no == rid.page_no) {
                file_hdr_.first_free_page_no = page_handle.page_hdr->next_free_page_no;
            }
            page_handle.page_hdr->next_free_page_no = RM_NO_PAGE;
            // Persist the updated file header immediately if first_free_page_no was changed
            // Note: This condition might need to be more general if other parts of file_hdr_ could change here.
            // For now, specifically targeting the first_free_page_no update.
            disk_manager_->write_page(fd_, RM_FILE_HDR_PAGE, (char*)&file_hdr_, sizeof(file_hdr_));
        }
    }
    
    // 标记页面为脏页
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
}

/**
 * @description: 删除记录文件中记录号为rid的记录
 * @param {Rid&} rid 要删除的记录的记录号（位置）
 * @param {Context*} context
 */
void RmFileHandle::delete_record(const Rid &rid, Context *context) {
    // 1. 获取指定记录所在的page handle
    auto &&page_handle = fetch_page_handle(rid.page_no);
    if (!Bitmap::is_set(page_handle.bitmap, rid.slot_no)) {
        throw RecordNotFoundError(rid.page_no, rid.slot_no);
    }
    
    // 2. MVCC: 获取现有记录并保存到撤销日志
    char* slot = page_handle.get_slot(rid.slot_no);
    RmRecord existing_record(file_hdr_.record_size, slot);
    TupleMeta existing_meta = extract_tuple_meta(existing_record);
    
    // 3. MVCC: 创建删除操作的撤销日志
    undo_ptr_t new_undo_ptr = INVALID_UNDO_PTR;
    if (context && context->txn_ && context->undo_mgr_) {
        // 提取用户数据部分用于撤销日志
        auto user_data = get_user_data(existing_record);
        
        // 创建撤销日志记录，保存被删除记录的完整数据
        new_undo_ptr = context->undo_mgr_->CreateUndoLog(
            UndoLogType::DELETE,
            existing_meta.creator_txn_id_,  // 原记录的创建者事务ID
            existing_meta.prev_version_ptr_, // 指向更早的版本
            user_data->size,
            user_data->data
        );
    }
    
    // 4. MVCC: 创建删除标记的TupleMeta
    TupleMeta delete_meta;
    if (context && context->txn_) {
        delete_meta.ts_ = context->txn_->get_read_ts();
        delete_meta.is_deleted_ = true;  // 标记为删除
        delete_meta.creator_txn_id_ = context->txn_->get_transaction_id();
        delete_meta.prev_version_ptr_ = new_undo_ptr;  // 指向撤销日志
    } else {
        delete_meta = existing_meta;
        delete_meta.is_deleted_ = true;
    }
    
    // 5. 保留用户数据，但更新TupleMeta为删除标记
    auto user_data = get_user_data(existing_record);
    auto full_record = create_full_record(user_data->data, delete_meta);
    memcpy(slot, full_record->data, file_hdr_.record_size);
    
    // 注意：在MVCC中，我们不立即释放slot，而是标记为删除
    // 删除的记录仍然占用空间，直到垃圾回收
    
    // 6. 标记页面为脏页
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
}

/**
 * @description: 更新记录文件中记录号为rid的记录
 * @param {Rid&} rid 要更新的记录的记录号（位置）
 * @param {char*} buf 新记录的数据
 * @param {Context*} context
 */
void RmFileHandle::update_record(const Rid &rid, char *buf, Context *context) {
    // 1. 获取指定记录所在的page handle
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    
    // 2. 检查记录是否存在
    if (!Bitmap::is_set(page_handle.bitmap, rid.slot_no)) {
        // 记录不存在，直接返回
        buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), false);
        return;
    }
    
    // 3. MVCC: 获取现有记录并提取TupleMeta
    char* slot = page_handle.get_slot(rid.slot_no);
    RmRecord existing_record(file_hdr_.record_size, slot);
    TupleMeta existing_meta = extract_tuple_meta(existing_record);
    
    // 4. MVCC: 创建更新操作的撤销日志
    undo_ptr_t new_undo_ptr = INVALID_UNDO_PTR;
    if (context && context->txn_ && context->undo_mgr_) {
        // 提取用户数据部分用于撤销日志
        auto user_data = get_user_data(existing_record);
        
        // 创建撤销日志记录，保存被更新记录的旧数据
        new_undo_ptr = context->undo_mgr_->CreateUndoLog(
            UndoLogType::UPDATE,
            existing_meta.creator_txn_id_,  // 原记录的创建者事务ID
            existing_meta.prev_version_ptr_, // 指向更早的版本
            user_data->size,
            user_data->data
        );
    }
    
    // 5. MVCC: 创建新版本的TupleMeta
    TupleMeta new_meta;
    if (context && context->txn_) {
        new_meta.ts_ = context->txn_->get_read_ts();
        new_meta.is_deleted_ = false;  // 更新操作不删除记录
        new_meta.creator_txn_id_ = context->txn_->get_transaction_id();
        new_meta.prev_version_ptr_ = new_undo_ptr;  // 指向撤销日志
    } else {
        new_meta = existing_meta;
    }
    
    // 6. 创建包含新TupleMeta的完整记录
    auto full_record = create_full_record(buf, new_meta);
    
    // 7. 更新记录
    memcpy(slot, full_record->data, file_hdr_.record_size);
    
    // 标记页面为脏页
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
}

/**
 * 以下函数为辅助函数，仅提供参考，可以选择完成如下函数，也可以删除如下函数，在单元测试中不涉及如下函数接口的直接调用
 */
/**
 * @description: 获取指定页面的页面句柄
 * @param {int} page_no 页面号
 * @return {RmPageHandle} 指定页面的句柄
 */
RmPageHandle RmFileHandle::fetch_page_handle(int page_no) const {
  // Todo:
  // 使用缓冲池获取指定页面，并生成page_handle返回给上层
  // if page_no is invalid, throw PageNotExistError exception

  // 使用缓冲池获取指定页面，并生成page_handle返回给上层
    // if page_no is invalid, throw PageNotExistError exception
    if (page_no >= file_hdr_.num_pages || page_no < 0) {
        throw PageNotExistError(page_no);
    }
    
    PageId page_id{fd_, page_no};
    Page* page = buffer_pool_manager_->fetch_page(page_id);
    if (page == nullptr) {
        throw PageNotExistError(page_no);
    }
    
    return RmPageHandle(&file_hdr_, page);
}

/**
 * @description: 创建一个新的page handle
 * @return {RmPageHandle} 新的PageHandle
 */
RmPageHandle RmFileHandle::create_new_page_handle() {
  // Todo:
  // 1.使用缓冲池来创建一个新page
  // 2.更新page handle中的相关信息
  // 3.更新file_hdr_

  // 1.使用缓冲池来创建一个新page
    PageId new_page_id{fd_, INVALID_PAGE_ID};
    Page* page = buffer_pool_manager_->new_page(&new_page_id);
    if (page == nullptr) {
        throw PageAllocationError(INVALID_PAGE_ID);
    }
    
    // 2.更新page handle中的相关信息
    RmPageHandle page_handle(&file_hdr_, page);
    
    // 初始化page_hdr
    page_handle.page_hdr->next_free_page_no = RM_NO_PAGE;
    page_handle.page_hdr->num_records = 0;
    
    // 初始化bitmap，全部设为0（表示所有slot都为空）
    Bitmap::init(page_handle.bitmap, file_hdr_.num_records_per_page);
    
    // 3.更新file_hdr_
    // 把新页添加到空闲页链表的头部
    page_handle.page_hdr->next_free_page_no = file_hdr_.first_free_page_no;
    file_hdr_.first_free_page_no = new_page_id.page_no;
    file_hdr_.num_pages++;
    
    // Update DiskManager's knowledge of the number of pages
    disk_manager_->set_fd2pageno(fd_, file_hdr_.num_pages);
    // Persist the updated file header
    disk_manager_->write_page(fd_, RM_FILE_HDR_PAGE, (char*)&file_hdr_, sizeof(file_hdr_));
    
    return page_handle;
}

/**
 * @brief 创建或获取一个空闲的page handle
 *
 * @return RmPageHandle 返回生成的空闲page handle
 * @note pin the page, remember to unpin it outside!
 */
RmPageHandle RmFileHandle::create_page_handle() {
  // Todo:
  // 1. 判断file_hdr_中是否还有空闲页
  //     1.1
  //     没有空闲页：使用缓冲池来创建一个新page；可直接调用create_new_page_handle()
  //     1.2 有空闲页：直接获取第一个空闲页
  // 2. 生成page handle并返回给上层

  // 1. 判断file_hdr_中是否还有空闲页
    if (file_hdr_.first_free_page_no == RM_NO_PAGE) {
        // 1.1 没有空闲页：使用缓冲池来创建一个新page
        return create_new_page_handle();
    } else {
        // 1.2 有空闲页：直接获取第一个空闲页
        // 2. 生成page handle并返回给上层
        return fetch_page_handle(file_hdr_.first_free_page_no);
    }
}

/**
 * @description:
 * 当一个页面从没有空闲空间的状态变为有空闲空间状态时，更新文件头和页头中空闲页面相关的元数据
 */
void RmFileHandle::release_page_handle(RmPageHandle &page_handle) {
  // Todo:
  // 当page从已满变成未满，考虑如何更新：
  // 1. page_handle.page_hdr->next_free_page_no
  // 2. file_hdr_.first_free_page_no
  // 当page从已满变成未满，需要将其添加到空闲页链表中
    
    // 1. page_handle.page_hdr->next_free_page_no
    // 将当前页的next_free_page_no指向原先的first_free_page_no
    page_handle.page_hdr->next_free_page_no = file_hdr_.first_free_page_no;
    
    // 2. file_hdr_.first_free_page_no
    // 将file_hdr_的first_free_page_no更新为当前页的页号
    file_hdr_.first_free_page_no = page_handle.page->get_page_id().page_no;
    
    // 更新文件头到磁盘
    disk_manager_->write_page(fd_, RM_FILE_HDR_PAGE, (char*)&file_hdr_, sizeof(file_hdr_));
    
    // 标记页面为脏页
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), true);
}

// MVCC: TupleMeta操作辅助方法实现

/**
 * @brief 从记录中提取TupleMeta
 */
TupleMeta RmFileHandle::extract_tuple_meta(const RmRecord& record) const {
    if (record.size < static_cast<int>(sizeof(TupleMeta))) {
        // 如果记录太小，返回默认的TupleMeta（兼容旧格式）
        return TupleMeta();
    }
    
    TupleMeta meta;
    memcpy(&meta, record.data, sizeof(TupleMeta));
    return meta;
}

/**
 * @brief 设置记录的TupleMeta
 */
void RmFileHandle::set_tuple_meta(RmRecord& record, const TupleMeta& meta) const {
    if (record.size >= static_cast<int>(sizeof(TupleMeta))) {
        memcpy(record.data, &meta, sizeof(TupleMeta));
    }
}

/**
 * @brief 获取记录的用户数据部分
 */
std::unique_ptr<RmRecord> RmFileHandle::get_user_data(const RmRecord& record) const {
    if (record.size <= static_cast<int>(sizeof(TupleMeta))) {
        // 记录太小，返回空记录
        return std::make_unique<RmRecord>(0);
    }
    
    int user_data_size = record.size - sizeof(TupleMeta);
    char* user_data_start = record.data + sizeof(TupleMeta);
    
    return std::make_unique<RmRecord>(user_data_size, user_data_start);
}

/**
 * @brief 创建包含TupleMeta的完整记录
 */
std::unique_ptr<RmRecord> RmFileHandle::create_full_record(const char* user_data, const TupleMeta& meta) const {
    int full_size = sizeof(TupleMeta) + file_hdr_.user_record_size;
    auto full_record = std::make_unique<RmRecord>(full_size);
    
    // 设置TupleMeta
    memcpy(full_record->data, &meta, sizeof(TupleMeta));
    
    // 设置用户数据
    if (user_data != nullptr && file_hdr_.user_record_size > 0) {
        memcpy(full_record->data + sizeof(TupleMeta), user_data, file_hdr_.user_record_size);
    }
    
    return full_record;
}