/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "rm_scan.h"
#include "rm_file_handle.h"

/**
 * @brief 初始化file_handle和rid
 * @param file_handle
 */
RmScan::RmScan(const RmFileHandle *file_handle) : file_handle_(file_handle) {
    // 文件头页是第0页，实际数据从第1页开始。
    // file_hdr_.num_pages 记录了包括头页在内的总页数。
    if (file_handle_->file_hdr_.num_pages <= 1) {
        // 如果总页数小于等于1，意味着没有数据页（只有头页或文件为空）。
        // 此时扫描应立即结束。
        rid_.page_no = RM_NO_PAGE; // 这将使得 is_end() 返回 true
        rid_.slot_no = 0; // 具体值不重要，因为已经结束
    } else {
        // 文件中存在数据页，从第一个数据页（页号为1）开始扫描。
        rid_.page_no = 1;  // 第一个数据页的页号
        rid_.slot_no = -1; // next() 会将 slot_no 加 1，从第一个槽位开始检查
        next();            // 调用 next() 寻找第一个有效记录
    }
}

/**
 * @brief 找到文件中下一个存放了记录的位置
 */
void RmScan::next() {
    // Todo:
    // 找到文件中下一个存放了记录的非空闲位置，用rid_来指向这个位置
    // 找到文件中下一个存放了记录的非空闲位置，用rid_来指向这个位置
    if (rid_.page_no >= file_handle_->file_hdr_.num_pages) {
        rid_.page_no = RM_NO_PAGE;
        return;
    }
    
    // 从当前槽位的下一个开始查找
    rid_.slot_no++;
    // 在当前页面中查找下一个有效记录
    while (true) {
        // 获取当前页面的页面句柄
        RmPageHandle page_handle = file_handle_->fetch_page_handle(rid_.page_no);
        // 在当前页面中寻找有效记录
        int num_slots_per_page = file_handle_->file_hdr_.num_records_per_page;
        while (rid_.slot_no < num_slots_per_page) {
            if (Bitmap::is_set(page_handle.bitmap, rid_.slot_no)) {
                // 找到有效记录，释放页面并返回
                file_handle_->buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), false);
                return;
            }
            rid_.slot_no++;
        }
        // 当前页面没有找到更多有效记录，释放页面
        file_handle_->buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(), false);
        // 移动到下一页
        rid_.page_no++;
        rid_.slot_no = 0;
        // 检查是否已经扫描完所有页面
        if (rid_.page_no >= file_handle_->file_hdr_.num_pages) {
            rid_.page_no = RM_NO_PAGE;
            break;
        }
    }
}

/**
 * @brief ​ 判断是否到达文件末尾
 */
bool RmScan::is_end() const {
    // Todo: 修改返回值
    // 如果页号超出了总页数，表示扫描结束
    return rid_.page_no == RM_NO_PAGE;
}

/**
 * @brief RmScan内部存放的rid
 */
Rid RmScan::rid() const {
    return rid_;
}