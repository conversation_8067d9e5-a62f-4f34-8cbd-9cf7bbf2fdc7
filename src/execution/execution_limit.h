/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class LimitExecutor : public AbstractExecutor {
   private:
    std::unique_ptr<AbstractExecutor> prev_;
    int limit_count_;                           // LIMIT的数量
    int returned_count_;                        // 已经返回的记录数量
    bool is_end_;

   public:
    LimitExecutor(std::unique_ptr<AbstractExecutor> prev, int limit_count) {
        prev_ = std::move(prev);
        limit_count_ = limit_count;
        returned_count_ = 0;
        is_end_ = false;
    }

    void beginTuple() override {
        prev_->beginTuple();
        returned_count_ = 0;
        is_end_ = false;
    }

    void nextTuple() override {
        if (returned_count_ < limit_count_ && !prev_->is_end()) {
            prev_->nextTuple();
        } else {
            is_end_ = true;
        }
    }

    std::unique_ptr<RmRecord> Next() override {
        if (is_end() || returned_count_ >= limit_count_) {
            return nullptr;
        }

        auto record = prev_->Next();
        if (record) {
            returned_count_++;
            if (returned_count_ >= limit_count_) {
                is_end_ = true;
            }
        } else {
            is_end_ = true;
        }
        
        return record;
    }

    Rid &rid() override {
        return prev_->rid();
    }

    // Returns the schema of the output records.
    // LimitExecutor does not change the schema of its child.
    const std::vector<ColMeta>& cols() const override {
        if (!prev_) {
            throw std::runtime_error("LimitExecutor has no child executor (prev_ is null).");
        }
        return prev_->cols();
    }

    bool is_end() const override {
        return is_end_ || returned_count_ >= limit_count_ || prev_->is_end();
    }

    std::string getType() override { 
        return "LimitExecutor"; 
    }

    ColMeta get_col_offset(const TabCol &target) override {
        return prev_->get_col_offset(target);
    }
};
