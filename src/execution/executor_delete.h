/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "transaction/mvcc_helper.h"
#include "transaction/undo_log_manager.h"
#include "transaction/txn_defs.h"
#include "execution/executor_update.h"  // for WriteConflictError
#include "errors.h"

class DeleteExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;                   // 表的元数据
    std::vector<Condition> conds_;  // delete的条件
    RmFileHandle *fh_;              // 表的数据文件句柄
    std::vector<Rid> rids_;         // 需要删除的记录的位置
    std::string tab_name_;          // 表名称
    SmManager *sm_manager_;

   public:
    DeleteExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<Condition> conds,
                   std::vector<Rid> rids, Context *context) {
        sm_manager_ = sm_manager;
        tab_name_ = tab_name;
        tab_ = sm_manager_->db_.get_table(tab_name);
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        conds_ = conds;
        rids_ = rids;
        context_ = context;
    }

    std::unique_ptr<RmRecord> Next() override {
      for (auto &rid : rids_) {
        std::unique_ptr<RmRecord> rm_record(fh_->get_record(rid, context_));
        
        // MVCC - 增强的写入冲突检测和逻辑删除
        if (context_->txn_->get_txn_mode()) {
            // 提取当前记录的TupleMeta
            TupleMeta current_meta = MVCCHelper::ExtractTupleMeta(*rm_record, rid);
            
            // 增强的写入冲突检测
            bool has_conflict = MVCCHelper::CheckWriteConflict(current_meta, context_->txn_, context_->txn_mgr_);
            if (has_conflict) {
                // 根据官方要求：触发冲突的事务主动abort
                // 抛出TransactionAbortException，由rmdb.cpp统一处理abort逻辑和输出
                throw TransactionAbortException(context_->txn_->get_transaction_id(), AbortReason::DEADLOCK_PREVENTION);
            }
            
            // 检查记录可见性 - 确保当前事务能看到要删除的记录
            bool is_visible = MVCCHelper::IsVisible(current_meta, context_->txn_, context_->txn_mgr_);
            if (!is_visible) {
                // 记录不可见，可能需要版本重构
                RmRecord visible_record;
                TupleMeta visible_meta;
                
                // 尝试从版本链重构可见版本
                bool found_visible = MVCCHelper::ReconstructTuple(*rm_record, current_meta, 
                                                                 context_->txn_, context_->txn_mgr_, 
                                                                 context_->undo_mgr_, 
                                                                 visible_record, visible_meta);
                if (!found_visible) {
                    // 没有找到可见版本，跳过此记录
                    continue;
                }
                
                // 使用重构的可见版本
                rm_record = std::make_unique<RmRecord>(visible_record);
                current_meta = visible_meta;
            }
            
            // 检查记录是否已被删除
            if (current_meta.is_deleted_) {
                // 记录已被删除，跳过
                continue;
            }
            
            // 在MVCC中，删除操作通过rm_file_handle的delete_record方法处理
            // 该方法会自动创建撤销日志并设置正确的TupleMeta
            
            // 记录删除操作的撤销日志（在delete_record中自动处理）
            WriteRecord* wr = new WriteRecord(WType::DELETE_TUPLE, tab_name_, rid, *rm_record);
            context_->txn_->append_write_record(wr);
            
            // 执行逻辑删除（通过rm_file_handle处理）
            fh_->delete_record(rid, context_);
            
            // 减少记录数量
            sm_manager_->decrement_record_count(tab_name_);
            
            continue;  // 跳过物理删除，进行下一个记录
        }
        
        // 非MVCC模式下的物理删除操作（保持原有逻辑）
        std::unordered_map<std::string, std::unordered_set<PageId>>
            index_page_ids;
        // 删除索引中的对应记录
        std::unique_ptr<char[]> key;
        for (auto &[index_name, index] : tab_.indexes) {
          auto ih = sm_manager_->ihs_
                        .at(sm_manager_->get_ix_manager()->get_index_name(
                            tab_name_, index.cols))
                        .get();

          key.reset(new char[index.col_tot_len]);
          int offset = 0;
          for (size_t i = 0; i < index.col_num; ++i) {
            memcpy(key.get() + offset, rm_record->data + index.cols[i].offset,
                   index.cols[i].len);
            offset += index.cols[i].len;
          }

          // index_page_ids.emplace(ih->find_node_page_id(key, context_->txn_),
          // index_name);
          ih->delete_entry(key.get(), context_->txn_);
        }
        //删除操作的撤销日志，确保删除操作在事务范围内可逆
        if (context_->txn_->get_txn_mode()) {
          WriteRecord* wr = new WriteRecord(WType::DELETE_TUPLE, tab_name_, rid, *std::move(rm_record));
          context_->txn_->append_write_record(wr);
        }
        fh_->delete_record(rid, context_);
        
        // 减少记录数量
        sm_manager_->decrement_record_count(tab_name_);
      }
      return nullptr;
    }

    Rid &rid() override { return _abstract_rid; }
};