#pragma once

#include "common/common.h"
#include "parser/ast.h"
#include "system/sm.h"
#include "record_printer.h"

/**
 * @description: 表达式计算器，用于在执行阶段计算表达式的值
 */
class ExpressionEvaluator {
public:
    /**
     * @description: 计算表达式的值
     * @param {shared_ptr<ast::Expr>} expr 需要计算的表达式
     * @param {RmRecord*} record 当前记录，用于获取列值
     * @param {TabMeta&} tab_meta 表元数据，用于列信息查找
     * @return {Value} 计算结果
     */
    static Value evaluate(const std::shared_ptr<ast::Expr> &expr, 
                         const RmRecord *record, 
                         const TabMeta &tab_meta);

private:
    /**
     * @description: 从记录中读取列值
     * @param {string} col_name 列名
     * @param {RmRecord*} record 记录
     * @param {TabMeta&} tab_meta 表元数据
     * @return {Value} 列值
     */
    static Value get_column_value(const std::string &col_name,
                                 const RmRecord *record,
                                 const TabMeta &tab_meta);

    /**
     * @description: 执行算术运算
     * @param {Value} lhs 左操作数
     * @param {SvArithOp} op 运算符
     * @param {Value} rhs 右操作数
     * @return {Value} 运算结果
     */
    static Value perform_arithmetic(const Value &lhs, ast::SvArithOp op, const Value &rhs);
}; 