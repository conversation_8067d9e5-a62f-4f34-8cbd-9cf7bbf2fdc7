/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include <climits>
#include <cfloat>
#include <utility>

class AggregationExecutor : public AbstractExecutor {
private:
    TabMeta table_metadata; // table schema information
    std::vector<std::shared_ptr<ast::Col> > column_definitions_;
    std::vector<Condition> filter_conditions_; // filtering criteria
    RmFileHandle *file_handle; // data file handler
    std::string table_name; // table identifier
    std::vector<Rid> record_ids; // storage for record identifiers
    SmManager *storage_manager; // storage system manager pointer
    std::vector<Value> value_list_; // value storage
    std::vector<ColMeta> result_column_metas; // output column metadata storage
    bool execution_finished_; // execution completion flag
    std::shared_ptr<ast::GroupBy> group_by_clause_; // grouping query information
    std::vector<std::pair<std::string, std::vector<Value>>> aggregation_results_; // all group results storage
    size_t current_result_index_; // current group position

public:
    AggregationExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<Condition> conds,
                        std::vector<std::shared_ptr<ast::Col> > cols, std::shared_ptr<ast::GroupBy> collect_group_bys,
                        std::vector<Rid> rids, Context *context) {
        this->column_definitions_ = std::move(cols);
        this->storage_manager = sm_manager;
        this->table_name = tab_name;
        this->table_metadata = storage_manager->db_.get_table(tab_name);
        this->file_handle = storage_manager->fhs_.at(tab_name).get();
        this->filter_conditions_ = std::move(conds);
        this->group_by_clause_ = std::move(collect_group_bys);
        this->record_ids = std::move(rids);
        context_ = context;

        setup_column_metadata();
        initialize_execution_state();
    }

private:
    void setup_column_metadata() {
        int byte_offset = 0;
        for (const auto &column_def: column_definitions_) {
            if (column_def->is_aggregation) {
                process_aggregation_column(column_def, byte_offset);
            } else {
                process_regular_column(column_def, byte_offset);
            }
        }
    }

    void process_aggregation_column(const std::shared_ptr<ast::Col> &col_def, int &offset_position) {
        if (col_def->ag_type.value() == ast::SV_AGGRE_COUNT) {
            ColMeta count_column_meta = {
                .tab_name = table_name,
                .name = col_def->as_name.has_value() ? col_def->as_name.value() : "COUNT(*)",
                .type = TYPE_INT, .len = sizeof(int), .offset = offset_position,
                .index = false
            };
            result_column_metas.push_back(count_column_meta);
            offset_position += sizeof(int);
        } else {
            ColMeta agg_column_meta = *table_metadata.get_col(col_def->col_name);
            agg_column_meta.name = col_def->as_name.has_value() ? col_def->as_name.value() : agg_column_meta.name;
            agg_column_meta.offset = offset_position;
            // aggregate function results are typically float type
            if (is_float_result_type(col_def->ag_type.value())) {
                agg_column_meta.type = TYPE_FLOAT;
                agg_column_meta.len = sizeof(float);
            }
            result_column_metas.push_back(agg_column_meta);
            offset_position += agg_column_meta.len;
        }
    }

    void process_regular_column(const std::shared_ptr<ast::Col> &col_def, int &offset_position) {
        ColMeta regular_column_meta = *table_metadata.get_col(col_def->col_name);
        regular_column_meta.offset = offset_position;
        result_column_metas.push_back(regular_column_meta);
        offset_position += regular_column_meta.len;
    }

    bool is_float_result_type(ast::SvAggregationType agg_type) {
        // 只有AVG总是返回浮点类型
        // SUM、MAX和MIN应该保持原始列的数据类型
        return (agg_type == ast::SV_AGGRE_AVG);
    }

    void initialize_execution_state() {
        value_list_.clear();
        aggregation_results_.clear();
        current_result_index_ = 0;
        execution_finished_ = false;
    }

public:
    std::unique_ptr<RmRecord> Next() override {
        if (current_result_index_ >= aggregation_results_.size()) {
            return std::make_unique<RmRecord>(0, nullptr);
        }

        const auto& current_result_values = aggregation_results_[current_result_index_].second;
        return build_record_from_values(current_result_values);
    }

private:
    std::unique_ptr<RmRecord> build_record_from_values(const std::vector<Value>& value_collection) {
        int total_bytes = 0;
        for (const auto &value_item: value_collection) {
            total_bytes += value_item.raw->size;
        }

        std::vector<char> data_buffer(total_bytes);
        char *buffer_pointer = data_buffer.data();

        for (const auto &value_item: value_collection) {
            std::memcpy(buffer_pointer, value_item.raw->data, value_item.raw->size);
            buffer_pointer += value_item.raw->size;
        }

        return std::make_unique<RmRecord>(total_bytes, data_buffer.data());
    }

public:
    Rid &rid() override { return _abstract_rid; }

    std::vector<Value> vals() { return value_list_; }

    std::string getType() { return "AggregationExecutor"; };

    void beginTuple() override {
        std::unordered_map<std::string, std::vector<Rid> > group_map; // storage for grouping results

        perform_record_grouping(group_map);

        // 如果特殊情况已处理，直接返回
        if (handle_special_cases(group_map)) {
            return;
        }

        process_groups_and_generate_results(group_map);
    }

private:
    void perform_record_grouping(std::unordered_map<std::string, std::vector<Rid> >& group_map) {
        // perform grouping operation
        for (const auto &record_id: record_ids) {
            auto record_data = file_handle->get_record(record_id, context_);
            std::string grouping_key;
            if (group_by_clause_ && group_by_clause_->is_group_by) {
                grouping_key = build_group_key(record_data);
            }
            group_map[grouping_key].push_back(record_id);
        }
    }

    std::string build_group_key(std::unique_ptr<RmRecord>& record_data) {
        std::string key_string;
        for (const auto &group_column: group_by_clause_->group_by_name_) {
            auto column_meta = table_metadata.get_col(group_column);
            int data_offset = column_meta->offset;
            char *field_data = record_data->data + data_offset;
            if (column_meta->type == TYPE_INT) {
                int int_value = *reinterpret_cast<int *>(field_data);
                key_string += std::to_string(int_value) + "|";
            } else if (column_meta->type == TYPE_FLOAT) {
                float float_value = *reinterpret_cast<float *>(field_data);
                key_string += std::to_string(float_value) + "|";
            } else if (column_meta->type == TYPE_STRING) {
                key_string += std::string(field_data, column_meta->len) + "|";
            }
        }
        return key_string;
    }

    bool handle_special_cases(std::unordered_map<std::string, std::vector<Rid> >& group_map) {
        // handle special case: COUNT only with no GROUP BY and no data
        if (check_count_only_query(column_definitions_) && record_ids.empty()) {
            std::vector<Value> count_values;
            for (const auto &column_def: column_definitions_) {
                Value count_result;
                switch (column_def->ag_type.value()) {
                    case ast::SV_AGGRE_COUNT: {
                        count_result.set_int(0);
                        count_result.init_raw(sizeof(int));
                        count_values.push_back(count_result);
                        break;
                    }
                    default:
                        break;
                }
            }
            aggregation_results_.emplace_back("", count_values);
            execution_finished_ = false;  // 设置为false，表示有结果可以返回
            return true;  // 返回true表示已处理特殊情况
        }
        return false;  // 返回false表示没有特殊情况
    }

    bool check_count_only_query(const std::vector<std::shared_ptr<ast::Col>>& column_list) {
        for (const auto &column_def: column_list) {
            if (column_def->is_aggregation && column_def->ag_type.value() != ast::SV_AGGRE_COUNT) {
                return false;
            }
        }
        return true;
    }

    void process_groups_and_generate_results(std::unordered_map<std::string, std::vector<Rid> >& group_map) {
        // sort group keys to ensure consistent output order
        std::vector<std::pair<std::string, std::vector<Rid>>> sorted_group_list(group_map.begin(), group_map.end());
        std::sort(sorted_group_list.begin(), sorted_group_list.end(),
                  [](const auto& group_a, const auto& group_b) { return group_a.first < group_b.first; });

        // apply HAVING filter before group calculation
        std::vector<std::pair<std::string, std::vector<Rid>>> filtered_group_list;
        if (group_by_clause_ && group_by_clause_->is_having_conds) {
            filtered_group_list = apply_having_conditions(sorted_group_list);
        } else {
            filtered_group_list = sorted_group_list;
        }

        compute_final_aggregation_results(filtered_group_list);
    }

    std::vector<std::pair<std::string, std::vector<Rid>>> apply_having_conditions(
        const std::vector<std::pair<std::string, std::vector<Rid>>>& group_list) {
        std::vector<std::pair<std::string, std::vector<Rid>>> filtered_results;
        for (const auto &group_item: group_list) {
            bool satisfies_having = true;
            for (const auto &having_condition: group_by_clause_->having_conds_) {
                auto rhs_value_ptr = std::dynamic_pointer_cast<ast::Value>(having_condition->rhs);
                Value rhs_val = convert_ast_value(rhs_value_ptr);
                Value lhs_val = calculate_agg_value(group_item.second, having_condition->lhs->ag_type.value(),
                                                   having_condition->lhs->col_name);
                if (!value_cmp(lhs_val, rhs_val, convert_comparison_operator(having_condition->op))) {
                    satisfies_having = false;
                    break;
                }
            }
            if (satisfies_having) {
                filtered_results.push_back(group_item);
            }
        }
        return filtered_results;
    }

    void compute_final_aggregation_results(const std::vector<std::pair<std::string, std::vector<Rid>>>& group_list) {
        // recalculate aggregation results for filtered groups only
        aggregation_results_.clear();
        for (const auto &group_item: group_list) {
            std::vector<Value> result_values;
            for (const auto &column_def: column_definitions_) {
                Value computed_value;
                if (column_def->is_aggregation) {
                    computed_value = calculate_agg_value(group_item.second, column_def->ag_type.value(), column_def->col_name);
                } else {
                    // handle non-aggregation columns
                    computed_value = extract_non_aggregation_value(group_item.second.front(), column_def->col_name);
                }
                result_values.push_back(computed_value);
            }
            // add computed results and group key to aggregation results
            aggregation_results_.emplace_back(group_item.first, result_values);
        }

        // set completion status based on results
        if (aggregation_results_.empty()) {
            execution_finished_ = true;
        } else {
            execution_finished_ = false;
        }
    }

    Value extract_non_aggregation_value(const Rid& record_id, const std::string& column_name) {
        auto column_meta = table_metadata.get_col(column_name);
        int data_offset = column_meta->offset;
        auto record_data = file_handle->get_record(record_id, context_);
        char *field_data = record_data->data + data_offset;

        Value extracted_value;
        if (column_meta->type == TYPE_INT) {
            int int_value = *reinterpret_cast<int *>(field_data);
            extracted_value.set_int(int_value);
            extracted_value.init_raw(sizeof(int));
        } else if (column_meta->type == TYPE_FLOAT) {
            float float_value = *reinterpret_cast<float *>(field_data);
            extracted_value.set_float(float_value);
            extracted_value.init_raw(sizeof(float));
        } else if (column_meta->type == TYPE_STRING) {
            std::string string_value(field_data, column_meta->len);
            extracted_value.set_str(string_value);
            extracted_value.init_raw(string_value.size());
        }
        return extracted_value;
    }

    Value convert_ast_value(const std::shared_ptr<ast::Value> &ast_value) {
        Value converted_value;
        if (auto integer_literal = std::dynamic_pointer_cast<ast::IntLit>(ast_value)) {
            converted_value.set_int(integer_literal->val);
        } else if (auto float_literal = std::dynamic_pointer_cast<ast::FloatLit>(ast_value)) {
            converted_value.set_float(float_literal->val);
        } else if (auto string_literal = std::dynamic_pointer_cast<ast::StringLit>(ast_value)) {
            converted_value.set_str(string_literal->val);
        } else {
            throw InternalError("Unexpected ast value type");
        }
        return converted_value;
    }

    CompOp convert_comparison_operator(ast::SvCompOp operator_type) {
        std::map<ast::SvCompOp, CompOp> operator_mapping = {
            {ast::SV_OP_EQ, OP_EQ}, {ast::SV_OP_NE, OP_NE}, {ast::SV_OP_LT, OP_LT},
            {ast::SV_OP_GT, OP_GT}, {ast::SV_OP_LE, OP_LE}, {ast::SV_OP_GE, OP_GE},
        };
        return operator_mapping.at(operator_type);
    }


public:
    void nextTuple() override {
        current_result_index_++;
        if (current_result_index_ >= aggregation_results_.size()) {
            execution_finished_ = true;
        }
    }

    bool is_end() const override {
        return execution_finished_;
    };

    const std::vector<ColMeta> &cols() const {
        return result_column_metas;
    };

    ColMeta get_col_offset(const TabCol &target) override {
        return *get_col(result_column_metas, target);
    }

    Value calculate_agg_value(const std::vector<Rid> &rids, ast::SvAggregationType type, const std::string &col_name) {
        Value result;
        switch (type) {
            case ast::SV_AGGRE_COUNT: {
                int count = rids.size();
                result.set_int(static_cast<int>(count));
                result.init_raw(sizeof(int));
                break;
            }
            case ast::SV_AGGRE_SUM: {
                if (col_name.empty()) {
                    // 对于空列名，返回0
                    result.set_int(0);
                    result.init_raw(sizeof(int));
                    break;
                }
                auto col_meta = table_metadata.get_col(col_name);
                int offset = col_meta->offset;

                if (col_meta->type == TYPE_INT) {
                    // INT 列求和，返回 INT 结果
                    int sum_value = 0;
                    for (const auto &rid: rids) {
                        auto rec = file_handle->get_record(rid, context_);
                        char *data = rec->data + offset;
                        int value = *reinterpret_cast<int *>(data);
                        sum_value += value;
                    }
                    result.set_int(sum_value);
                    result.init_raw(sizeof(int));
                } else if (col_meta->type == TYPE_FLOAT) {
                    // FLOAT 列求和，返回 FLOAT 结果
                    float sum_value = 0.0f;
                    for (const auto &rid: rids) {
                        auto rec = file_handle->get_record(rid, context_);
                        char *data = rec->data + offset;
                        float value = *reinterpret_cast<float *>(data);
                        sum_value += value;
                    }
                    result.set_float(sum_value);
                    result.init_raw(sizeof(float));
                }
                break;
            }
            case ast::SV_AGGRE_MAX: {
                if (col_name.empty()) {
                    // 对于空列名，返回0
                    result.set_float(0.0f);
                    result.init_raw(sizeof(float));
                    break;
                }
                auto col_meta = table_metadata.get_col(col_name);
                int offset = col_meta->offset;
                if (rids.empty()) {
                    if (col_meta->type == TYPE_INT) {
                        result.set_int(0);
                        result.init_raw(sizeof(int));
                    } else {
                        result.set_float(0.0f);
                        result.init_raw(sizeof(float));
                    }
                    break;
                }

                if (col_meta->type == TYPE_INT) {
                    // 处理整数类型的MAX
                    auto first_rec = file_handle->get_record(rids[0], context_);
                    char *first_data = first_rec->data + offset;
                    int max_value = *reinterpret_cast<int *>(first_data);

                    for (const auto &rid: rids) {
                        auto rec = file_handle->get_record(rid, context_);
                        char *data = rec->data + offset;
                        int value = *reinterpret_cast<int *>(data);
                        if (value > max_value) {
                            max_value = value;
                        }
                    }
                    result.set_int(max_value);
                    result.init_raw(sizeof(int));
                } else if (col_meta->type == TYPE_FLOAT) {
                    // 处理浮点类型的MAX
                    auto first_rec = file_handle->get_record(rids[0], context_);
                    char *first_data = first_rec->data + offset;
                    float max_value = *reinterpret_cast<float *>(first_data);

                    for (const auto &rid: rids) {
                        auto rec = file_handle->get_record(rid, context_);
                        char *data = rec->data + offset;
                        float value = *reinterpret_cast<float *>(data);
                        if (value > max_value) {
                            max_value = value;
                        }
                    }
                    result.set_float(max_value);
                    result.init_raw(sizeof(float));
                } else {
                    // 不支持的类型
                    result.set_float(0.0f);
                    result.init_raw(sizeof(float));
                }
                break;
            }
            case ast::SV_AGGRE_MIN: {
                if (col_name.empty()) {
                    // 对于空列名，返回0
                    result.set_float(0.0f);
                    result.init_raw(sizeof(float));
                    break;
                }
                auto col_meta = table_metadata.get_col(col_name);
                int offset = col_meta->offset;
                if (rids.empty()) {
                    if (col_meta->type == TYPE_INT) {
                        result.set_int(0);
                        result.init_raw(sizeof(int));
                    } else {
                        result.set_float(0.0f);
                        result.init_raw(sizeof(float));
                    }
                    break;
                }

                if (col_meta->type == TYPE_INT) {
                    // 处理整数类型的MIN
                    auto first_rec = file_handle->get_record(rids[0], context_);
                    char *first_data = first_rec->data + offset;
                    int min_value = *reinterpret_cast<int *>(first_data);

                    for (const auto &rid: rids) {
                        auto rec = file_handle->get_record(rid, context_);
                        char *data = rec->data + offset;
                        int value = *reinterpret_cast<int *>(data);
                        if (value < min_value) {
                            min_value = value;
                        }
                    }
                    result.set_int(min_value);
                    result.init_raw(sizeof(int));
                } else if (col_meta->type == TYPE_FLOAT) {
                    // 处理浮点类型的MIN
                    auto first_rec = file_handle->get_record(rids[0], context_);
                    char *first_data = first_rec->data + offset;
                    float min_value = *reinterpret_cast<float *>(first_data);

                    for (const auto &rid: rids) {
                        auto rec = file_handle->get_record(rid, context_);
                        char *data = rec->data + offset;
                        float value = *reinterpret_cast<float *>(data);
                        if (value < min_value) {
                            min_value = value;
                        }
                    }
                    result.set_float(min_value);
                    result.init_raw(sizeof(float));
                } else {
                    // 不支持的类型
                    result.set_float(0.0f);
                    result.init_raw(sizeof(float));
                }
                break;
            }
            case ast::SV_AGGRE_AVG: {
                if (col_name.empty()) {
                    // 对于空列名，返回0
                    result.set_float(0.0f);
                    result.init_raw(sizeof(float));
                    break;
                }
                auto col_meta = table_metadata.get_col(col_name);
                int offset = col_meta->offset;
                double sum_value = 0.0;
                int count = rids.size();

                for (const auto &rid: rids) {
                    auto rec = file_handle->get_record(rid, context_);
                    char *data = rec->data + offset;
                    if (col_meta->type == TYPE_INT) {
                        int value = *reinterpret_cast<int *>(data);
                        sum_value += static_cast<double>(value);
                    } else if (col_meta->type == TYPE_FLOAT) {
                        float value = *reinterpret_cast<float *>(data);
                        sum_value += static_cast<double>(value);
                    }
                }

                double avg_value = (count > 0) ? (sum_value / count) : 0.0;
                result.set_float(static_cast<float>(avg_value));
                result.init_raw(sizeof(float));
                break;
            }
            default:
                break;
        }
        return result;
    }
};
