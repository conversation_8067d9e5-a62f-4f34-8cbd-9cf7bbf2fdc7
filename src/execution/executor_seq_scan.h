/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "transaction/mvcc_helper.h"
#include "transaction/undo_log_manager.h"

class SeqScanExecutor : public AbstractExecutor
{
private:
    std::string tab_name_;             // 表的名称
    std::vector<Condition> conds_;     // scan的条件
    RmFileHandle *fh_;                 // 表的数据文件句柄
    std::vector<ColMeta> cols_;        // scan后生成的该记录的字段元数据数组
    size_t len_;                       // scan后生成的每条记录的长度
    std::vector<Condition> fed_conds_; // 同conds_，两个字段相同

    Rid rid_;
    std::unique_ptr<RecScan> scan_; // table_iterator

    SmManager *sm_manager_;

    // bool table_locked;
    
    /**
     * @brief 检查当前记录对事务是否可见（MVCC）
     * @param rid 记录位置
     * @param visible_record 输出参数，可见版本的记录
     * @return true 如果找到可见版本，false 否则
     */
    bool checkMVCCVisibility(const Rid& rid, std::unique_ptr<RmRecord>& visible_record) {
        // 获取原记录
        auto current_record = fh_->get_record(rid, context_);
        if (!current_record) {
            return false;
        }
        
        // MVCC: 完整的可见性检查和版本重构
        if (context_ && context_->txn_) {
            // 提取当前版本的TupleMeta
            TupleMeta current_meta = fh_->extract_tuple_meta(*current_record);
            
            // 检查当前版本是否可见
            if (MVCCHelper::IsVisible(current_meta, context_->txn_, context_->txn_mgr_)) {
                // 如果当前版本可见且未被删除，直接返回
                if (!current_meta.is_deleted_) {
                    visible_record = std::move(current_record);
                    return true;
                }
                // 如果当前版本已被删除，返回不可见（在快照隔离中，删除的记录不应该出现在扫描结果中）
                return false;
            }
            
            // 当前版本不可见，尝试重构可见版本
            if (context_->undo_mgr_) {
                RmRecord reconstructed_record;
                TupleMeta visible_meta;
                
                bool found = MVCCHelper::ReconstructTuple(
                    *current_record, current_meta, context_->txn_,
                    context_->txn_mgr_, context_->undo_mgr_,
                    reconstructed_record, visible_meta
                );
                
                if (found && !visible_meta.is_deleted_) {
                    visible_record = std::make_unique<RmRecord>(reconstructed_record);
                    return true;
                }
            }
            
            // 没有找到可见版本
            return false;
        }
        
        // 如果没有事务上下文，直接返回原记录（兼容旧逻辑）
        visible_record = std::move(current_record);
        return true;
    }

public:
    SeqScanExecutor(SmManager *sm_manager, std::string tab_name,
                    std::vector<Condition> conds, Context *context)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        // std::cout << "Creating SeqScanExecutor" << std::flush;
        // std::cout << std::endl;
        sm_manager_ = sm_manager;
        tab_name_ = std::move(tab_name);
        conds_ = std::move(conds);
        TabMeta &tab = sm_manager_->db_.get_table(tab_name_);
        fh_ = sm_manager_->fhs_.at(tab_name_).get();
        cols_ = tab.cols;
        len_ = cols_.back().offset + cols_.back().len;

        context_ = context;

        fed_conds_ = conds_;
    }

    // 判断当前record是否满足SQL中condition的要求
    bool handleCondition(std::vector<ColMeta> &cols, Condition &cond, char *data) const
    {
        // 通过列名在 cols 中查找 左边的字段 的元数据信息
        auto lhs_col_meta = *std::find_if(cols.begin(), cols.end(), [&](const ColMeta &col_meta)
                                          { return col_meta.name == cond.lhs_col.col_name; });

        // 计算左值数据在记录中的位置
        const char *lhs_data = data + lhs_col_meta.offset;
        const char *rhs_data = nullptr;

        // 声明cond右边的值/字段
        Value rhs_val;
        ColMeta rhs_col_meta;

        // 如果 cond 的右边是 值
        if (cond.is_rhs_val)
        {
            rhs_val = cond.rhs_val;

            // 检查左右值类型是否兼容
            if ((lhs_col_meta.type == TYPE_STRING && rhs_val.type != TYPE_STRING) ||
                (lhs_col_meta.type != TYPE_STRING && rhs_val.type == TYPE_STRING))
            {
                throw IncompatibleTypeError(lhs_col_meta.name, "rhs_val");
            }
        }
        // 如果 cond 的右边是 字段
        else
        {
            rhs_col_meta = *std::find_if(cols.begin(), cols.end(), [&](ColMeta &col_meta)
                                         { return col_meta.name == cond.rhs_col.col_name; });
            rhs_data = data + rhs_col_meta.offset;

            // 检查左边字段和右边值类型是否兼容
            if ((lhs_col_meta.type == TYPE_STRING && rhs_col_meta.type != TYPE_STRING) ||
                (lhs_col_meta.type != TYPE_STRING && rhs_col_meta.type == TYPE_STRING))
            {
                throw IncompatibleTypeError(lhs_col_meta.name, rhs_col_meta.name);
            }
        }

        std::string str(lhs_data, lhs_col_meta.len);

        auto comparator = [lhs_data, lhs_col_meta, rhs_data, rhs_col_meta, &cond](const Value &rhs_val, ColType lhs_type, CompOp op) -> bool
        {
            switch (lhs_type)
            {
            case TYPE_INT:
                switch (op)
                {
                case OP_EQ:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) == rhs_val.int_val
                                           : (float)*(int *)(lhs_data) == (float)*(int *)(rhs_data);
                    break;
                case OP_NE:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) != rhs_val.int_val
                                           : (float)*(int *)(lhs_data) != (float)*(int *)(rhs_data);
                    break;
                case OP_LT:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) < rhs_val.int_val
                                           : (float)*(int *)(lhs_data) < (float)*(int *)(rhs_data);
                case OP_LE:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) <= rhs_val.int_val
                                           : (float)*(int *)(lhs_data) <= (float)*(int *)(rhs_data);
                case OP_GT:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) > rhs_val.int_val
                                           : (float)*(int *)(lhs_data) > (float)*(int *)(rhs_data);
                case OP_GE:
                    return cond.is_rhs_val ? (float)*(int *)(lhs_data) >= rhs_val.int_val
                                           : (float)*(int *)(lhs_data) >= (float)*(int *)(rhs_data);
                }
            case TYPE_FLOAT:
                switch (op)
                {
                case OP_EQ:
                    return cond.is_rhs_val ? *(float *)(lhs_data) == rhs_val.float_val
                                           : *(float *)(lhs_data) == *(float *)(rhs_data);
                    break;
                case OP_NE:
                    return cond.is_rhs_val ? *(float *)(lhs_data) != rhs_val.float_val
                                           : *(float *)(lhs_data) != *(float *)(rhs_data);
                case OP_LT:
                    return cond.is_rhs_val ? *(float *)(lhs_data) < rhs_val.float_val
                                           : *(float *)(lhs_data) < *(float *)(rhs_data);
                case OP_LE:
                    return cond.is_rhs_val ? *(float *)(lhs_data) <= rhs_val.float_val
                                           : *(float *)(lhs_data) <= *(float *)(rhs_data);
                case OP_GT:
                    return cond.is_rhs_val ? *(float *)(lhs_data) > rhs_val.float_val
                                           : *(float *)(lhs_data) > *(float *)(rhs_data);
                case OP_GE:
                    return cond.is_rhs_val ? *(float *)(lhs_data) >= rhs_val.float_val
                                           : *(float *)(lhs_data) >= *(float *)(rhs_data);
                }
            case TYPE_STRING:
                switch (op)
                {
                case OP_EQ:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) == 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) == 0;
                case OP_NE:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) != 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) != 0;
                case OP_LT:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) < 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) < 0;
                case OP_LE:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) <= 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) <= 0;
                case OP_GT:
                    return cond.is_rhs_val ? strncmp(lhs_data,
                                                     rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) > 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) > 0;
                case OP_GE:
                    return cond.is_rhs_val ? strncmp(lhs_data, rhs_val.str_val.c_str(),
                                                     std::max(lhs_col_meta.len, (int)rhs_val.str_val.size())) >= 0
                                           : strncmp(lhs_data, rhs_data, std::max(lhs_col_meta.len, rhs_col_meta.len)) >= 0;
                }; // End of switch(op) for TYPE_STRING
                // No break needed here as all inner paths should return.
            default:
                // Handle unsupported types to prevent control reaching end of non-void lambda
                throw InternalError("Unsupported column type in SeqScanExecutor condition evaluation: " + std::to_string(lhs_type));
            } // End of switch (lhs_type)
        }; // End of lambda comparator

        return comparator(rhs_val, lhs_col_meta.type, cond.op);
    }

    // 用于初始化扫描过程，设置初始状态，并找到第一个满足条件的记录。
    void
    beginTuple() override
    {
        /**
         * 顺序扫描加锁算法：
         * (Original locking logic L198-L232 is preserved)
         */
        TabMeta tab_meta = sm_manager_->db_.get_table(tab_name_);
        if (tab_meta.indexes.empty() || conds_.size() == 0)
        {
            context_->lock_mgr_->lock_shared_on_table(context_->txn_, sm_manager_->fhs_.at(tab_name_)->GetFd());
        }
        else
        {
            // Original gap lock logic placeholder
        }

        scan_ = std::make_unique<RmScan>(fh_); // RmScan constructor calls its own next()
                                             // to position at the first physical record or set is_end().
        
        // Loop to find the first record that satisfies conditions.
        while (true) {
            if (scan_->is_end()) {
                // No physical records found from the beginning, or all have been checked.
                // is_end() will correctly return true. rid_ is not updated to an invalid state.
                return;
            }

            // Scan is not at the end, so its current rid is a candidate.
            rid_ = scan_->rid(); // Update our rid_

            // MVCC: 首先检查记录可见性
            std::unique_ptr<RmRecord> visible_record;
            if (!checkMVCCVisibility(rid_, visible_record)) {
                // 记录不可见，继续下一条
                scan_->next();
                continue;
            }

            bool fit_cond = true;
            // 注意：visible_record包含TupleMeta，需要跳过TupleMeta部分获取用户数据
            char *data = visible_record->data + sizeof(TupleMeta);
            for (auto &cond : conds_) {
                if (!handleCondition(cols_, cond, data)) {
                    fit_cond = false;
                    break;
                }
            }

            if (fit_cond) {
                // Found the first record satisfying conditions.
                // rid_ is correctly set. is_end() will be false.
                // Locking for the specific record (if any) would go here.
                // context_->lock_mgr_->lock_shared_on_record(context_->txn_, rid_, fh_->GetFd());
                return;
            }

            // Condition not met, advance scan_ to the next physical record and try again.
            scan_->next();
        }
    }
    // 用于在扫描过程中继续扫描表中的下一个记录，并找到下一个满足条件的记录。
    void nextTuple() override
    {
        if (is_end()) { // If scan already ended (e.g. from beginTuple or previous nextTuple), do nothing
            return;
        }

        while (true) {
            scan_->next(); // Advance the underlying scan to the next physical record

            if (scan_->is_end()) {
                // Scan reached the end. rid_ should not be updated with an invalid scan_->rid().
                // The is_end() method (which calls scan_->is_end()) will now correctly report true.
                return;
            }

            // Scan is not at the end, so update our rid_ to this potential candidate
            rid_ = scan_->rid();

            // MVCC: 首先检查记录可见性
            std::unique_ptr<RmRecord> visible_record;
            if (!checkMVCCVisibility(rid_, visible_record)) {
                // 记录不可见，继续下一条
                continue;
            }

            bool fit_cond = true;
            // 注意：visible_record包含TupleMeta，需要跳过TupleMeta部分获取用户数据
            char *data = visible_record->data + sizeof(TupleMeta);
            for (auto &cond : conds_) {
                if (!handleCondition(cols_, cond, data)) {
                    fit_cond = false;
                    break;
                }
            }

            if (fit_cond) {
                // Found a record that satisfies conditions. rid_ is now set to it.
                // is_end() will be false (as scan_->is_end() was false).
                return;
            }
            // Condition not met, loop will call scan_->next() again at the beginning of the next iteration.
        }
    }

    /**
     * 从文件中获取下一条记录。
     * 该函数重写了基类的Next()方法，用于在文件中顺序读取并返回下一条记录。
     *
     * @return std::unique_ptr<RmRecord> 返回一个指向读取的记录的智能指针。
     */
    std::unique_ptr<RmRecord> Next() override
    {
        if (is_end()) { // Check if the scan has reached the end (is_end() calls scan_->is_end())
            return nullptr;
        }

        // MVCC: 获取对当前事务可见的记录版本
        std::unique_ptr<RmRecord> visible_record;
        if (!checkMVCCVisibility(rid_, visible_record)) {
            return nullptr;
        }

        // 分配内存以存储解析后的数据
        char *data = new char[len_];

        // 初始化数据内存块为0
        memset(data, 0, len_);

        // 遍历列定义，将每列数据从可见记录复制到新分配的内存中
        // 注意：visible_record包含TupleMeta，需要跳过TupleMeta部分获取用户数据
        char* user_data_start = visible_record->data + sizeof(TupleMeta);
        size_t offset = 0;
        for (auto &col : cols_)
        {
            memcpy(data + offset, user_data_start + col.offset, col.len);
            offset += col.len;
        }

        // 断言确保所有列数据都已正确复制，内存布局与原始记录匹配
        assert(offset == len_);

        // 创建并返回一个新的RmRecord对象，使用新分配的内存存储数据
        std::unique_ptr<RmRecord> record = std::make_unique<RmRecord>(len_, data);

        // 由于record对象已接管数据指针，原始分配的内存可以释放
        delete[] data;

        return record;
    }

    /**
     * 根据预定义的列信息和数据缓冲区，构造并返回一个包含多个Value对象的向量。
     * 每个Value对象代表一行数据中的一列，Value对象的类型根据列的类型来确定。
     *
     * @return 返回一个包含根据列信息构造的Value对象的向量。
     */
    // std::vector<Value> constructVal() override
    // {
    //     // 分配足够空间的缓冲区，用于存储所有列的数据。
    //     char *buf = new char[len_ + 1];

    //     memcpy(buf, fh_->fetch_page_handle(rid_.page_no).get_slot(rid_.slot_no), len_);

    //     Value val;              // 用于临时存储每列数据的Value对象。
    //     std::vector<Value> vec; // 存储最终构造的Value对象数组。
    //     // 遍历每一列，分别处理。
    //     for (const auto &col : cols_)
    //     {
    //         // 为当前列分配空间，并复制数据到该空间。
    //         char dest[col.len + 1];
    //         memcpy(dest, buf + col.offset, col.len);
    //         dest[col.len] = '\0'; // 确保字符串以空字符结尾。

    //         // 根据当前列的类型，设置Value对象的值。
    //         val.type = col.type;
    //         switch (col.type)
    //         {
    //         case TYPE_INT:
    //             val.set_int(*(int *)dest);
    //             break;
    //         case TYPE_FLOAT:
    //             val.set_float(*(float *)dest);
    //             break;
    //         case TYPE_STRING:
    //             val.set_str(dest);
    //             break;
    //         }
    //         // 将处理好的Value对象添加到结果向量中。
    //         vec.emplace_back(val);
    //     }
    //     // 释放之前分配的缓冲区。
    //     delete[] buf;
    //     return vec; // 返回构造好的Value对象数组。
    // }

    Rid &rid() override { return rid_; }

    bool is_end() const override { return scan_->is_end(); }

    const std::vector<ColMeta> &cols() const override { return cols_; }

    ColMeta get_col_offset(const TabCol &target) override { return *get_col(cols_, target); }

    size_t tupleLen() const override { return len_; }

    std::string getType() override { return "SeqScanExecutor"; }

    // const std::vector<Condition> get_conds() const override { return conds_; }
};
