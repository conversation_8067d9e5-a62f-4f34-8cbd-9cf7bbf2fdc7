/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class ProjectionExecutor : public AbstractExecutor {
   private:
    std::unique_ptr<AbstractExecutor> prev_;        // 投影节点的儿子节点
    std::vector<ColMeta> cols_;                     // 需要投影的字段
    size_t len_;                                    // 字段总长度
    std::vector<size_t> sel_idxs_;                  

   public:
    ProjectionExecutor(std::unique_ptr<AbstractExecutor> prev, const std::vector<TabCol> &sel_cols) {
        prev_ = std::move(prev);

        size_t curr_offset = 0;
        auto &prev_cols = prev_->cols();
        for (auto &sel_col : sel_cols) {
            auto pos = get_col(prev_cols, sel_col);
            sel_idxs_.push_back(pos - prev_cols.begin());
            auto col = *pos;
            col.offset = curr_offset;
            curr_offset += col.len;
            cols_.push_back(col);
        }
        len_ = curr_offset;
    }

    void beginTuple() override
    {
        prev_->beginTuple();
    }

    void nextTuple() override
    {
        prev_->nextTuple();
    }


    std::unique_ptr<RmRecord> Next() override
    {
        
        //std::cout<<"???"<<getType()<<std::flush;
        // 获取前一个执行器的下一条记录

        std::unique_ptr<RmRecord> tuple(prev_->Next());

        if (tuple == nullptr) { // If the child executor has no more tuples, projection also ends.
            return nullptr;
        }

        // 初始化变量 (Original comment from L60)
        // 用于存储新记录中的各列数据和最终拼接的字符串 (Original comment from L61)

        // for (int i = 0; i < sizeof(tuple.get()->data) / sizeof(tuple.get()->data[0]); ++i) (Original L63-L67)
        // {
        //     std::cout << tuple.get()->data[i]<<std::flush;
        //     std::cout << std::endl;
        // }
        char *data = new char[len_]; // Now tuple is guaranteed not to be nullptr
        size_t offset = 0;
        memset(data, 0, len_);
        // 构建投影记录
        for (auto &col : cols_)
        {
            TabCol tab_col{col.tab_name, col.name};
            auto old_col = prev_->get_col_offset(tab_col); // This call might be problematic if prev_ schema changes or is complex

            // std::cout <<"===========================" <<std::flush;
            // std::cout << std::endl;
            // std::cout << "tab_name:"<<old_col.tab_name<<std::flush;
            // std::cout << std::endl;
            // std::cout << "col_name:"<<old_col.name<<std::flush;
            // std::cout << std::endl;
            // std::cout <<"===========================" <<std::flush;
            // std::cout << std::endl;

            memcpy(data + offset, tuple->data + old_col.offset, old_col.len);
            // std::cout << "???" << old_col.len << std::flush;
            // std::cout << std::endl;
            offset += old_col.len;
        }

        // std::cout << offset << std::flush;
        // std::cout << std::endl;

        // std::cout << "=========" << len_ << "==========" << std::flush;
        // std::cout << std::endl;

        assert(offset == len_);
        // 创建新记录并返回
        RmRecord rec(len_);
        rec.SetData(data);
        delete[] data;
        return std::make_unique<RmRecord>(rec);
    }



    Rid &rid() override { return _abstract_rid; }

        size_t tupleLen() const override { return len_; }

    std::string getType() override { return "ProjectionExecutor"; }

    bool is_end() const override { return prev_->is_end(); }

    const std::vector<ColMeta> &cols() const override { return cols_; }

    ColMeta get_col_offset(const TabCol &target) override { return *get_col(cols_, target); }

};