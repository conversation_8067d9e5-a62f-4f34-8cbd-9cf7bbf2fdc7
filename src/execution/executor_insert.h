/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "transaction/mvcc_helper.h"
#include "transaction/txn_defs.h"
#include "transaction/transaction_manager.h"
#include "record/rm_scan.h"
#include "transaction/mvcc_helper.h"
#include "transaction/undo_log_manager.h"

class InsertExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;                   // 表的元数据
    std::vector<Value> values_;     // 需要插入的数据
    RmFileHandle *fh_;              // 表的数据文件句柄
    std::string tab_name_;          // 表名称
    Rid rid_;                       // 插入的位置，由于系统默认插入时不指定位置，因此当前rid_在插入后才赋值
    SmManager *sm_manager_;
    bool inserted_;                 // Flag to track if the insert operation has been performed

   public:
    InsertExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<Value> values, Context *context) : inserted_(false) {
        sm_manager_ = sm_manager;
        tab_ = sm_manager_->db_.get_table(tab_name);
        values_ = values;
        tab_name_ = tab_name;
        if (values.size() != tab_.cols.size()) {
            throw InvalidValueCountError();
        }
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
    };

    std::unique_ptr<RmRecord> Next() override {
        // Make record buffer
        RmRecord rec(fh_->get_file_hdr().record_size);
        for (size_t i = 0; i < values_.size(); i++) {
            auto &col = tab_.cols[i];
            auto &val = values_[i];
            if (col.type != val.type) {
                throw IncompatibleTypeError(coltype2str(col.type), coltype2str(val.type));
            }
            val.init_raw(col.len);
            memcpy(rec.data + col.offset, val.raw->data, col.len);
        }
        
        // TODO: MVCC - 为新记录设置版本标记
        // 在完整MVCC实现中，这里应该：
        // 1. 设置 creator_txn_id 为当前事务ID
        // 2. 设置 prev_version_ptr 为无效值
        // 当前暂时跳过，等待阶段一的TupleMeta完全集成

        // MVCC: Check for write conflicts with concurrent operations
        if (context_->txn_->get_txn_mode()) {
            // 方法1：检查已提交的记录（磁盘上的记录）
            for (auto scan = std::make_unique<RmScan>(fh_); !scan->is_end(); scan->next()) {
                auto rid = scan->rid();
                auto existing_record = fh_->get_record(rid, context_);
                TupleMeta existing_meta = MVCCHelper::ExtractTupleMeta(*existing_record, rid);

                // 只检查由其他事务创建的记录
                if (existing_meta.creator_txn_id_ == context_->txn_->get_transaction_id()) {
                    continue;  // 跳过自己创建的记录
                }

                // 获取现有记录的用户数据
                auto existing_user_data = MVCCHelper::GetUserData(*existing_record);

                // 检查是否是相同的记录内容
                bool same_content = (existing_user_data->size == rec.size &&
                                   memcmp(existing_user_data->data, rec.data, rec.size) == 0);

                if (same_content) {
                    // 检查写冲突（无论记录是否被删除）
                    bool has_conflict = MVCCHelper::CheckWriteConflict(existing_meta, context_->txn_, context_->txn_mgr_);
                    if (has_conflict) {
                        throw TransactionAbortException(context_->txn_->get_transaction_id(), AbortReason::DEADLOCK_PREVENTION);
                    }
                }
            }


        }

        // Check for uniqueness constraint violations before inserting
        for(const auto& [index_name, index] : tab_.indexes) {
            auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
            char* key = new char[index.col_tot_len];
            int offset = 0;
            for(size_t j = 0; j < index.col_num; ++j) {
                memcpy(key + offset, rec.data + index.cols[j].offset, index.cols[j].len);
                offset += index.cols[j].len;
            }

            std::vector<Rid> result_rids;
            if (ih->get_value(key, &result_rids, context_->txn_)) {
                delete[] key;
                throw RMDBError("Duplicate key violates unique constraint");
            }
            delete[] key;
        }

        // Insert into record file
        rid_ = fh_->insert_record(rec.data, context_);

        //插入操作的撤销日志 - 先添加到写集合
        if (context_->txn_->get_txn_mode()) {
            WriteRecord* wr =new WriteRecord(WType::INSERT_TUPLE, tab_name_, rid_, rec);
            context_->txn_->append_write_record(wr);

            // 现在检查其他活跃事务的写集合（INSERT-INSERT冲突）
            auto& txn_map = TransactionManager::txn_map;
            for (const auto& [txn_id, other_txn] : txn_map) {
                // 跳过当前事务
                if (txn_id == context_->txn_->get_transaction_id()) {
                    continue;
                }

                // 只检查活跃事务
                if (other_txn->get_state() != TransactionState::DEFAULT &&
                    other_txn->get_state() != TransactionState::GROWING &&
                    other_txn->get_state() != TransactionState::SHRINKING) {
                    continue;
                }

                // 检查其他事务的写集合
                auto other_write_set = other_txn->get_write_set();
                for (const auto& write_record : *other_write_set) {
                    // 只检查INSERT操作
                    if (write_record->GetWriteType() == WType::INSERT_TUPLE &&
                        write_record->GetTableName() == tab_name_) {

                        // 检查是否是相同的记录内容
                        const auto& other_record = write_record->GetRecord();
                        if (other_record.size == rec.size &&
                            memcmp(other_record.data, rec.data, rec.size) == 0) {

                            // 发现INSERT-INSERT冲突，当前事务应该abort
                            throw TransactionAbortException(context_->txn_->get_transaction_id(), AbortReason::DEADLOCK_PREVENTION);
                        }
                    }
                }
            }
        }

        // Insert into index
        for(const auto& [index_name, index] : tab_.indexes) {
            auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
            char* key = new char[index.col_tot_len];
            int offset = 0;
            for(size_t j = 0; j < index.col_num; ++j) {
                memcpy(key + offset, rec.data + index.cols[j].offset, index.cols[j].len);
                offset += index.cols[j].len;
            }

            ih->insert_entry(key, rid_, context_->txn_);
            delete[] key;
        }

        // 增加记录数量
        sm_manager_->increment_record_count(tab_name_);
        inserted_ = true; // Mark as inserted
        return nullptr;   // Insert operation does not return a record
    }

    Rid &rid() override { return rid_; }

    void beginTuple() override {
        // For InsertExecutor, beginTuple might not need to do much.
        // If the executor were designed to be re-runnable for multiple sets of values (not typical for SQL INSERT),
        // then inserted_ would be reset here. For a one-shot SQL INSERT, this is fine.
        // inserted_ = false; // Reset if re-runnable
    }

    bool is_end() const override {
        return inserted_;
    }

    const std::vector<ColMeta>& cols() const override {
        // InsertExecutor does not produce output columns.
        static const std::vector<ColMeta> empty_cols;
        return empty_cols;
    }

    size_t tupleLen() const override {
        // InsertExecutor does not produce output tuples.
        return 0;
    }

    std::string getType() override { return "InsertExecutor"; }
};