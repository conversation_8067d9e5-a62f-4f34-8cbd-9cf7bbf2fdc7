#pragma once

#include "execution/executor_abstract.h"
#include "optimizer/plan.h" // For JoinPlan
#include "record/rm_defs.h" // For RmRecord (was rm_record.h)
#include "common/common.h" // For Condition, ColMeta etc.

#include <memory>
#include <vector>
#include <algorithm> // For std::all_of

class NestedLoopSemiJoinExecutor : public AbstractExecutor {
private:
    std::unique_ptr<AbstractExecutor> left_child_;
    std::unique_ptr<AbstractExecutor> right_child_;
    const JoinPlan* plan_; // Contains join conditions and type

    // Output schema is the schema of the left child
    std::vector<ColMeta> cols_; 
    size_t tuple_len_;

    // Current left tuple being processed
    std::unique_ptr<RmRecord> current_left_tuple_;
    bool left_tuple_consumed_; // Flag to indicate if current_left_tuple_ needs to be fetched

    bool is_end_;

private:
    // Helper methods for condition evaluation (can be adapted from NestedLoopJoinExecutor)
    template <typename T>
    bool compare_values(const T& lhs_val, const T& rhs_val, CompOp op) const {
        switch (op) {
            case OP_EQ: return lhs_val == rhs_val;
            case OP_NE: return lhs_val != rhs_val;
            case OP_LT: return lhs_val < rhs_val;
            case OP_GT: return lhs_val > rhs_val;
            case OP_LE: return lhs_val <= rhs_val;
            case OP_GE: return lhs_val >= rhs_val;
            default: return false;
        }
    }

    bool compare_string_result(int cmp_res, CompOp op) const {
        switch (op) {
            case OP_EQ: return cmp_res == 0;
            case OP_NE: return cmp_res != 0;
            case OP_LT: return cmp_res < 0;
            case OP_GT: return cmp_res > 0;
            case OP_LE: return cmp_res <= 0;
            case OP_GE: return cmp_res >= 0;
            default: return false;
        }
    }
    
    // Evaluates a single condition
    bool evaluate_condition(const RmRecord* left_tuple, const RmRecord* right_tuple, const Condition& cond) const {
        if (!left_tuple || !right_tuple) return false;

        // Find column metadata for lhs
        auto lhs_col_meta_it = std::find_if(left_child_->cols().begin(), left_child_->cols().end(),
                                       [&](const ColMeta& cm){ return cm.tab_name == cond.lhs_col.tab_name && cm.name == cond.lhs_col.col_name; });
        if (lhs_col_meta_it == left_child_->cols().end()) {
             // This should not happen if planner and analyzer are correct
            throw RMDBError("LHS column not found in left child schema for SEMI JOIN condition.");
        }
        const ColMeta& lhs_meta = *lhs_col_meta_it;
        char* lhs_data = left_tuple->data + lhs_meta.offset;

        char* rhs_data = nullptr;
        ColMeta rhs_meta; // Only used if !cond.is_rhs_val

        if (!cond.is_rhs_val) {
            auto rhs_col_meta_it = std::find_if(right_child_->cols().begin(), right_child_->cols().end(),
                                       [&](const ColMeta& cm){ return cm.tab_name == cond.rhs_col.tab_name && cm.name == cond.rhs_col.col_name; });
            if (rhs_col_meta_it == right_child_->cols().end()) {
                throw RMDBError("RHS column not found in right child schema for SEMI JOIN condition.");
            }
            rhs_meta = *rhs_col_meta_it;
            rhs_data = right_tuple->data + rhs_meta.offset;
        }

        switch (lhs_meta.type) {
            case TYPE_INT: {
                int lhs_val = *reinterpret_cast<int*>(lhs_data);
                int rhs_val;
                if (cond.is_rhs_val) rhs_val = cond.rhs_val.int_val;
                else rhs_val = *reinterpret_cast<int*>(rhs_data);
                return compare_values(lhs_val, rhs_val, cond.op);
            }
            case TYPE_FLOAT: {
                float lhs_val = *reinterpret_cast<float*>(lhs_data);
                float rhs_val;
                if (cond.is_rhs_val) rhs_val = cond.rhs_val.float_val;
                else rhs_val = *reinterpret_cast<float*>(rhs_data);
                return compare_values(lhs_val, rhs_val, cond.op);
            }
            case TYPE_STRING: {
                // Assuming null-terminated or fixed-length strings handled by RmRecord/ColMeta
                std::string lhs_str(lhs_data, strnlen(lhs_data, lhs_meta.len));
                std::string rhs_str;
                if (cond.is_rhs_val) rhs_str = cond.rhs_val.str_val;
                else rhs_str = std::string(rhs_data, strnlen(rhs_data, rhs_meta.len));
                return compare_string_result(lhs_str.compare(rhs_str), cond.op);
            }
            default:
                throw RMDBError("Unsupported column type in SEMI JOIN condition evaluation.");
        }
        return false;
    }

    // Checks if the given pair of tuples satisfies all join conditions
    bool check_join_conditions(const RmRecord* left_tuple, const RmRecord* right_tuple) const {
        if (plan_->conds_.empty()) return true; // No conditions means true (cross product behavior for the check)
        return std::all_of(plan_->conds_.begin(), plan_->conds_.end(), 
                           [&](const Condition& cond){ return evaluate_condition(left_tuple, right_tuple, cond); });
    }


public:
    NestedLoopSemiJoinExecutor(std::unique_ptr<AbstractExecutor> left_child,
                               std::unique_ptr<AbstractExecutor> right_child,
                               const JoinPlan* plan)
        : left_child_(std::move(left_child)),
          right_child_(std::move(right_child)),
          plan_(plan),
          left_tuple_consumed_(true), // Start by fetching the first left tuple
          is_end_(false) {
        
        if (plan_ == nullptr || plan_->type != ::SEMI_JOIN) {
            throw RMDBError("NestedLoopSemiJoinExecutor requires a SEMI_JOIN plan node.");
        }
        // Output schema is that of the left child
        cols_ = left_child_->cols();
        tuple_len_ = left_child_->tupleLen();
    }

    void beginTuple() override {
        left_child_->beginTuple();
        right_child_->beginTuple(); // Initialize right child as well
        is_end_ = false;
        left_tuple_consumed_ = true; // Ensure first left tuple is fetched by Next()
        current_left_tuple_ = nullptr;
    }

    // The Next() method for SEMI JOIN is different from regular JOIN
    std::unique_ptr<RmRecord> Next() override {
        if (is_end_) {
            return nullptr;
        }

        while (true) { // Outer loop for advancing left_child_
            if (left_tuple_consumed_) {
                // If current_left_tuple_ was valid from a previous iteration (i.e., not the very first call to Next after beginTuple)
                // and we are here to get a *new* left tuple, we must advance the left child first.
                if (current_left_tuple_ != nullptr) {
                    left_child_->nextTuple(); // Advance left child before getting its Next()
                }
                current_left_tuple_ = left_child_->Next(); // Get the (potentially new) current left tuple

                if (left_child_->is_end() && current_left_tuple_ == nullptr) { // Check if left child is truly exhausted
                    is_end_ = true;
                    return nullptr;
                }
                // This case can happen if left_child_->is_end() becomes true after nextTuple(), but Next() still returns last valid before end.
                // Or if is_end() is true, Next() should be null.
                if (current_left_tuple_ == nullptr) {
                    is_end_ = true; // Assume left child ended if Next() returns null
                    return nullptr;
                }

                left_tuple_consumed_ = false; // We have a new left tuple to process
                right_child_->beginTuple();   // Reset right child scan for the new left tuple
            }

            // Inner loop: scan right_child_ for a match with current_left_tuple_
            while (!right_child_->is_end()) {
                auto right_tuple = right_child_->Next(); // Get current right tuple

                if (right_tuple == nullptr) {
                    // This implies the end of the current scan of the right child for this iteration.
                    // Or an error in right_child_ if !is_end() but Next() is null.
                    // We'll break and let the outer loop decide if it needs a new left tuple.
                    break;
                }

                if (check_join_conditions(current_left_tuple_.get(), right_tuple.get())) {
                    // Match found!
                    left_tuple_consumed_ = true; // Signal to advance left child in the next outer loop iteration
                    this->_abstract_rid = left_child_->rid(); // Capture RID of the left tuple being outputted
                    auto result_tuple_data = std::make_unique<RmRecord>(tuple_len_);
                    memcpy(result_tuple_data->data, current_left_tuple_->data, tuple_len_);
                    
                    // After returning, the next call to this->Next() will use the fact that
                    // left_tuple_consumed_ is true to advance the left child.
                    return result_tuple_data;
                }
                
                right_child_->nextTuple(); // Advance right child to its next tuple if no match
            }

            // If we exit the inner while loop, it means right_child_ was exhausted for current_left_tuple_
            // without finding a match (or right_tuple was null and we broke).
            left_tuple_consumed_ = true; // Signal to advance left child in the next outer loop iteration.
            // Continue to the beginning of the outer while(true) loop to fetch the next left tuple.
        }
        // return nullptr; // Should be unreachable due to logic inside the loop
    }
    
    void nextTuple() override {
        // This method is part of the AbstractExecutor interface.
        // In this implementation, Next() advances to the next valid output tuple.
        // Calling nextTuple() could be interpreted as "discard current Next() result and prepare for another Next() call".
        // Or, it could be a way to signal the executor to internally advance its state
        // without returning a tuple, though this is less common with iterator-style Next().
        // Given that Next() finds the *next* tuple, this method might not be strictly needed
        // by users of the executor if they always consume the result of Next().
        // For now, let's assume it's not critical for the SEMI JOIN logic itself,
        // as Next() is self-contained in finding the subsequent output.
        // If a specific behavior is required by the execution engine, this might need implementation.
        // A simple approach is to make it a no-op or rely on the next Next() call.
    }


    bool is_end() const override {
        return is_end_;
    }

    // Schema information methods
    const std::vector<ColMeta>& cols() const override {
        return cols_;
    }

    size_t tupleLen() const override {
        return tuple_len_;
    }
    
    // Returns the RID of the last tuple returned by Next().
    // _abstract_rid is updated by Next() before returning a tuple.
    Rid& rid() override {
        return _abstract_rid;
    }

    ColMeta get_col_offset(const TabCol &target) override {
    auto it = std::find_if(cols_.begin(), cols_.end(), [&](const ColMeta& cm){
        return cm.tab_name == target.tab_name && cm.name == target.col_name;
    });
    if (it == cols_.end()) {
        throw RMDBError("Column " + target.tab_name + "." + target.col_name + " not found in NestedLoopSemiJoinExecutor schema.");
    }
    return *it;
    }

};