#include "expression_evaluator.h"
#include "errors.h"

Value ExpressionEvaluator::evaluate(const std::shared_ptr<ast::Expr> &expr,
                                    const RmRecord *record,
                                    const TabMeta &tab_meta) {
    if (auto val = std::dynamic_pointer_cast<ast::Value>(expr)) {
        // 直接返回值
        if (auto int_lit = std::dynamic_pointer_cast<ast::IntLit>(val)) {
            Value result;
            result.set_int(int_lit->val);
            return result;
        } else if (auto float_lit = std::dynamic_pointer_cast<ast::FloatLit>(val)) {
            Value result;
            result.set_float(float_lit->val);
            return result;
        } else if (auto str_lit = std::dynamic_pointer_cast<ast::StringLit>(val)) {
            Value result;
            result.set_str(str_lit->val);
            return result;
        } else if (auto bool_lit = std::dynamic_pointer_cast<ast::BoolLit>(val)) {
            Value result;
            result.set_int(bool_lit->val ? 1 : 0);
            return result;
        }
    } else if (auto col = std::dynamic_pointer_cast<ast::Col>(expr)) {
        // 从记录中读取列值
        return get_column_value(col->col_name, record, tab_meta);
    } else if (auto arith = std::dynamic_pointer_cast<ast::ArithmeticExpr>(expr)) {
        // 递归计算左右操作数，然后执行运算
        Value lhs = evaluate(arith->lhs, record, tab_meta);
        Value rhs = evaluate(arith->rhs, record, tab_meta);
        return perform_arithmetic(lhs, arith->op, rhs);
    }
    
    throw InternalError("Unsupported expression type in evaluation");
}

Value ExpressionEvaluator::get_column_value(const std::string &col_name,
                                           const RmRecord *record,
                                           const TabMeta &tab_meta) {
    // 查找列元数据
    TabMeta &non_const_tab_meta = const_cast<TabMeta&>(tab_meta);
    auto col_meta = non_const_tab_meta.get_col(col_name);

    Value result;
    
    // 根据列类型读取数据
    switch (col_meta->type) {
        case TYPE_INT: {
            int val = *(int*)(record->data + col_meta->offset);
            result.set_int(val);
            break;
        }
        case TYPE_FLOAT: {
            float val = *(float*)(record->data + col_meta->offset);
            result.set_float(val);
            break;
        }
        case TYPE_STRING: {
            char* str_data = (char*)(record->data + col_meta->offset);
            std::string val(str_data, col_meta->len);
            // 去除末尾的空字符
            val.erase(val.find_last_not_of('\0') + 1);
            result.set_str(val);
            break;
        }
        default:
            throw InternalError("Unsupported column type");
    }
    
    return result;
}

Value ExpressionEvaluator::perform_arithmetic(const Value &lhs, ast::SvArithOp op, const Value &rhs) {
    Value result;
    
    // 类型提升：如果一个是float，结果就是float
    bool result_is_float = (lhs.type == TYPE_FLOAT || rhs.type == TYPE_FLOAT);
    
    // 检查字符串不能参与算术运算
    if (lhs.type == TYPE_STRING || rhs.type == TYPE_STRING) {
        throw InternalError("String values cannot be used in arithmetic operations");
    }
    
    // 获取操作数值
    float lhs_val = (lhs.type == TYPE_FLOAT) ? lhs.float_val : (float)lhs.int_val;
    float rhs_val = (rhs.type == TYPE_FLOAT) ? rhs.float_val : (float)rhs.int_val;
    
    float result_val;
    
    // 执行运算
    switch (op) {
        case ast::SV_OP_ADD:
            result_val = lhs_val + rhs_val;
            break;
        case ast::SV_OP_SUB:
            result_val = lhs_val - rhs_val;
            break;
        case ast::SV_OP_MUL:
            result_val = lhs_val * rhs_val;
            break;
        case ast::SV_OP_DIV:
            if (rhs_val == 0.0f) {
                throw InternalError("Division by zero");
            }
            result_val = lhs_val / rhs_val;
            break;
        default:
            throw InternalError("Unsupported arithmetic operator");
    }
    
    // 设置结果类型和值
    if (result_is_float) {
        result.set_float(result_val);
    } else {
        result.set_int((int)result_val);
    }
    
    return result;
} 