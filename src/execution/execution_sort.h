/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class SortExecutor : public AbstractExecutor {
   private:
    std::unique_ptr<AbstractExecutor> prev_;
    std::vector<ColMeta> sort_cols_;            // 支持多个键排序的列元数据
    std::vector<bool> is_desc_list_;            // 每列对应的排序方向
    size_t tuple_num;
    std::vector<std::unique_ptr<RmRecord>> sorted_tuples_;  // 存储排序后的记录
    size_t current_index_;                      // 当前返回的记录索引
    bool is_end_;

   public:
    // 新的构造函数，支持多列排序
    SortExecutor(std::unique_ptr<AbstractExecutor> prev,
                 const std::vector<std::pair<TabCol, bool>>& sort_cols) {
        prev_ = std::move(prev);

        try {
            for (const auto& sort_col : sort_cols) {
                ColMeta col_meta = prev_->get_col_offset(sort_col.first);
                sort_cols_.push_back(col_meta);
                is_desc_list_.push_back(sort_col.second);
            }
        } catch (const std::exception& e) {
            throw;
        }

        tuple_num = 0;
        current_index_ = 0;
        is_end_ = false;
        sorted_tuples_.clear();
    }

    // 为了向后兼容，保留单列构造函数
    SortExecutor(std::unique_ptr<AbstractExecutor> prev, TabCol sel_cols, bool is_desc) {
        prev_ = std::move(prev);
        try {
            ColMeta col_meta = prev_->get_col_offset(sel_cols);
            sort_cols_.push_back(col_meta);
            is_desc_list_.push_back(is_desc);
        } catch (const std::exception& e) {
            throw;
        }
        tuple_num = 0;
        current_index_ = 0;
        is_end_ = false;
        sorted_tuples_.clear();
    }

    void beginTuple() override {
        // 收集所有记录
        sorted_tuples_.clear();
        current_index_ = 0;
        is_end_ = false;

        try {
            prev_->beginTuple();
            while (!prev_->is_end()) {
                auto record = prev_->Next();
                if (record && record->size > 0) {
                    // 创建记录的副本
                    auto record_copy = std::make_unique<RmRecord>(record->size);
                    memcpy(record_copy->data, record->data, record->size);
                    sorted_tuples_.push_back(std::move(record_copy));
                }
                prev_->nextTuple();
            }

            // 排序记录
            if (!sorted_tuples_.empty()) {
                std::sort(sorted_tuples_.begin(), sorted_tuples_.end(),
                          [this](const std::unique_ptr<RmRecord>& a, const std::unique_ptr<RmRecord>& b) {
                              return compare_records(a.get(), b.get());
                          });
            }

            tuple_num = sorted_tuples_.size();
        } catch (const std::exception& e) {
            throw;
        }
    }

    void nextTuple() override {
        current_index_++;
        if (current_index_ >= sorted_tuples_.size()) {
            is_end_ = true;
        }
    }

    std::unique_ptr<RmRecord> Next() override {
        if (current_index_ >= sorted_tuples_.size()) {
            return std::make_unique<RmRecord>(0, nullptr);
        }

        const auto& record = sorted_tuples_[current_index_];
        auto result = std::make_unique<RmRecord>(record->size);
        memcpy(result->data, record->data, record->size);
        return result;
    }

    Rid &rid() override {
        // TODO: Should return RID of the current sorted tuple.
        return _abstract_rid; // Placeholder
    }

    // Returns the schema of the output records.
    // SortExecutor does not change the schema of its child.
    const std::vector<ColMeta>& cols() const override {
        if (!prev_) {
            throw std::runtime_error("SortExecutor has no child executor (prev_ is null).");
        }
        return prev_->cols();
    }

    bool is_end() const override {
        return is_end_ || current_index_ >= sorted_tuples_.size();
    }

    std::string getType() override {
        return "SortExecutor";
    }

    ColMeta get_col_offset(const TabCol &target) override {
        return prev_->get_col_offset(target);
    }

private:
    // 比较两个记录的排序键（支持多列排序）
    bool compare_records(const RmRecord* a, const RmRecord* b) {
        try {
            // 检查记录的有效性
            if (!a || !b) {
                return false;
            }

            // 按顺序比较每个排序列
            for (size_t i = 0; i < sort_cols_.size(); ++i) {
                const ColMeta& col_meta = sort_cols_[i];
                bool is_desc = is_desc_list_[i];

                // 检查偏移量的有效性
                if (a->size <= col_meta.offset || b->size <= col_meta.offset) {
                    continue;  // 跳过无效的列
                }

                // 获取排序列的值
                char* data_a = a->data + col_meta.offset;
                char* data_b = b->data + col_meta.offset;

                int cmp_result = 0;
                switch (col_meta.type) {
                    case TYPE_INT: {
                        if (a->size < col_meta.offset + sizeof(int) || b->size < col_meta.offset + sizeof(int)) {
                            continue;
                        }
                        int val_a = *reinterpret_cast<int*>(data_a);
                        int val_b = *reinterpret_cast<int*>(data_b);
                        cmp_result = (val_a < val_b) ? -1 : (val_a > val_b) ? 1 : 0;
                        break;
                    }
                    case TYPE_FLOAT: {
                        if (a->size < col_meta.offset + sizeof(float) || b->size < col_meta.offset + sizeof(float)) {
                            continue;
                        }
                        float val_a = *reinterpret_cast<float*>(data_a);
                        float val_b = *reinterpret_cast<float*>(data_b);
                        cmp_result = (val_a < val_b) ? -1 : (val_a > val_b) ? 1 : 0;
                        break;
                    }
                    case TYPE_STRING: {
                        if (a->size < col_meta.offset + col_meta.len || b->size < col_meta.offset + col_meta.len) {
                            continue;
                        }
                        std::string val_a(data_a, col_meta.len);
                        std::string val_b(data_b, col_meta.len);
                        cmp_result = val_a.compare(val_b);
                        break;
                    }
                    default:
                        continue;  // 跳过未知类型
                }

                // 如果当前列的值不相等，根据排序方向返回结果
                if (cmp_result != 0) {
                    if (is_desc) {
                        return cmp_result > 0;  // 降序
                    } else {
                        return cmp_result < 0;  // 升序
                    }
                }
                // 如果当前列的值相等，继续比较下一列
            }

            // 所有列都相等，返回false（a不小于b）
            return false;
        } catch (const std::exception& e) {
            return false;
        }
    }
};