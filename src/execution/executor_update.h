/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "index/ix_index_handle.h"
#include "system/sm.h"
#include "expression_evaluator.h"
#include "transaction/mvcc_helper.h"
#include "transaction/undo_log_manager.h"
#include "transaction/txn_defs.h"
#include "errors.h"

// MVCC相关异常定义
class WriteConflictError : public RMDBError {
public:
    WriteConflictError(const std::string &msg) : RMDBError("Write conflict: " + msg) {}
};

class UpdateExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;
    std::vector<Condition> conds_;
    RmFileHandle *fh_;
    std::vector<Rid> rids_;
    std::string tab_name_;
    std::vector<SetClause> set_clauses_;
    SmManager *sm_manager_;
    bool updated_; // Flag to track if the update operation has been performed

   public:
    UpdateExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<SetClause> set_clauses,
                   std::vector<Condition> conds, std::vector<Rid> rids, Context *context) : updated_(false) {
        sm_manager_ = sm_manager;
        tab_name_ = tab_name;
        set_clauses_ = set_clauses;
        tab_ = sm_manager_->db_.get_table(tab_name);
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        conds_ = conds;
        rids_ = rids;
        context_ = context;
    }
    std::unique_ptr<RmRecord> Next() override {
// 锁定整个表以进行独占访问
        if (context_ && context_->lock_mgr_) { // Add null check for lock_mgr_ for testing
            context_->lock_mgr_->lock_exclusive_on_table(context_->txn_, fh_->GetFd());
        }

        // 不再在这里初始化，而是在每个记录处理时计算表达式

        // 遍历所有需要更新的记录ID
        for (auto& rid : rids_) {
            // 获取磁盘上的最新记录
            auto disk_record = fh_->get_record(rid, context_);
            std::unique_ptr<RmRecord> record;

            // MVCC - 增强的写入冲突检测和版本管理
            if (context_->txn_->get_txn_mode()) {
                // 提取磁盘记录的TupleMeta
                TupleMeta disk_meta = MVCCHelper::ExtractTupleMeta(*disk_record, rid);

                // 首先检查当前事务能否看到要更新的记录
                // 我们需要重构到当前事务可见的版本
                RmRecord visible_record;
                TupleMeta visible_meta;

                bool found_visible = MVCCHelper::ReconstructTuple(
                    *disk_record, disk_meta, context_->txn_, context_->txn_mgr_,
                    context_->undo_mgr_, visible_record, visible_meta
                );

                if (!found_visible || visible_meta.is_deleted_) {
                    // 检查是否是因为其他活跃事务的冲突导致记录不可见
                    bool has_conflict = MVCCHelper::CheckWriteConflict(disk_meta, context_->txn_, context_->txn_mgr_);
                    if (has_conflict) {
                        // 存在写写冲突，应该abort而不是静默跳过
                        throw TransactionAbortException(context_->txn_->get_transaction_id(), AbortReason::DEADLOCK_PREVENTION);
                    }
                    // 如果没有冲突，说明记录确实不存在或已被删除，可以跳过
                    continue;
                }

                // 基于可见版本进行写入冲突检测
                // 注意：我们应该检查磁盘上最新版本的冲突，因为我们要在最新版本上创建新版本
                bool has_conflict = MVCCHelper::CheckWriteConflict(disk_meta, context_->txn_, context_->txn_mgr_);
                if (has_conflict) {
                    // 根据官方要求：触发冲突的事务主动abort
                    // 抛出TransactionAbortException，由rmdb.cpp统一处理abort逻辑和输出
                    throw TransactionAbortException(context_->txn_->get_transaction_id(), AbortReason::DEADLOCK_PREVENTION);
                }

                // 使用可见的记录进行后续处理
                record = std::make_unique<RmRecord>(visible_record);
            } else {
                // 非MVCC模式，直接使用磁盘记录
                record = std::make_unique<RmRecord>(*disk_record);
            }
            
            // MVCC: 获取用户数据部分进行更新
            auto user_data = MVCCHelper::GetUserData(*record);
            
            // 创建一个新的用户数据记录对象，用于存储更新后的记录
            RmRecord new_user_record{ user_data->size };
            // 将原始用户数据拷贝到新记录中
            memcpy(new_user_record.data, user_data->data, user_data->size);

            // 遍历所有的 set_clauses_ 以应用更新
            for (auto& set_clause : set_clauses_) {
                // 获取列的元数据
                auto col_meta = tab_.get_col(set_clause.lhs.col_name);
                // 获取记录的长度
                int record_len = col_meta->len;
                // 获取记录的偏移量
                int record_offset = col_meta->offset;

                // 计算表达式的值
                Value new_value;
                if (set_clause.expr) {
                    // 如果有表达式，使用表达式计算器（基于用户数据）
                    new_value = ExpressionEvaluator::evaluate(set_clause.expr, user_data.get(), tab_);
                } else {
                    // 如果没有表达式，使用原来的rhs值（兼容性）
                    new_value = set_clause.rhs;
                }
                
                // 初始化新值的raw数据
                new_value.init_raw(record_len);

                // 检查列的类型并执行相应的转换
                if (col_meta->type == TYPE_INT && new_value.type == TYPE_FLOAT) {
                    int int_value = static_cast<int>(new_value.float_val);
                    memcpy(new_user_record.data + record_offset, &int_value, record_len);
                }
                else if (col_meta->type == TYPE_FLOAT && new_value.type == TYPE_INT) {
                    float float_value = static_cast<float>(new_value.int_val);
                    memcpy(new_user_record.data + record_offset, &float_value, record_len);
                }
                else if (col_meta->type == TYPE_STRING && new_value.type == TYPE_STRING) {
                    if (record_len < static_cast<int>(new_value.str_val.size())) {
                        throw StringOverflowError();
                    }
                    memset(new_user_record.data + record_offset, 0, record_len);
                    memcpy(new_user_record.data + record_offset, new_value.str_val.c_str(), new_value.str_val.size());
                }
                else if (col_meta->type == new_value.type) {
                    // 如果类型匹配，直接复制数据
                    memcpy(new_user_record.data + record_offset, new_value.raw->data, record_len);
                }
                else {
                    // 处理其他类型转换错误
                    /*throw TypeMismatchError(set_clause.lhs.col_name, col_meta->type, new_value.type);*/
                }
            }

            // First, check for any potential unique constraint violations before making any changes.
            for (const auto& [index_name, index] : tab_.indexes) {
                char* new_key = new char[index.col_tot_len];
                int offset = 0;
                for (size_t j = 0; j < index.col_num; ++j) {
                    memcpy(new_key + offset, new_user_record.data + index.cols[j].offset, index.cols[j].len);
                    offset += index.cols[j].len;
                }

                std::vector<Rid> result_rids;
                auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();

                // If a record with the new key already exists AND it's not the same record we are updating, it's a violation.
                if (ih->get_value(new_key, &result_rids, context_->txn_) && !result_rids.empty() && result_rids[0] != rid) {
                    delete[] new_key;
                    throw RMDBError("Duplicate key violates unique constraint on update");
                }
                delete[] new_key;
            }

            // All checks passed. Now, update indexes and the record.
            for (const auto& [index_name, index] : tab_.indexes) {
                auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
                
                char* old_key = new char[index.col_tot_len];
                char* new_key = new char[index.col_tot_len];
                
                int old_offset = 0;
                int new_offset = 0;

                std::vector<ColType> col_types;
                std::vector<int> col_lens;

                for (size_t j = 0; j < index.col_num; ++j) {
                    // 从原记录的用户数据部分提取旧键
                    auto old_user_data = MVCCHelper::GetUserData(*record);
                    memcpy(old_key + old_offset, old_user_data->data + index.cols[j].offset, index.cols[j].len);
                    old_offset += index.cols[j].len;
                    
                    // 从新用户数据中提取新键
                    memcpy(new_key + new_offset, new_user_record.data + index.cols[j].offset, index.cols[j].len);
                    new_offset += index.cols[j].len;

                    col_types.push_back(index.cols[j].type);
                    col_lens.push_back(index.cols[j].len);
                }

                // If the key part of the record was not changed, skip index update.
                if (ix_compare(old_key, new_key, col_types, col_lens) != 0) {
                    ih->delete_entry(old_key, context_->txn_);
                    ih->insert_entry(new_key, rid, context_->txn_);
                }
                
                delete[] old_key;
                delete[] new_key;
            }
            //在事务模式下记录更新操作（UPDATE）的撤销日志
            if (context_->txn_->get_txn_mode()) {
                // 撤销日志记录的是用户数据部分
                RmRecord rec{new_user_record.size};
                memcpy(rec.data, new_user_record.data, new_user_record.size);
                WriteRecord* wr =new WriteRecord(WType::UPDATE_TUPLE, tab_name_, rid, rec);
                // 旧记录也应该是用户数据部分
                auto old_user_data = MVCCHelper::GetUserData(*record);
                wr->old_record_ = *old_user_data;
                context_->txn_->append_write_record(wr);
            }
            // Finally, update the record in the table file (RmFileHandle会自动处理TupleMeta)
            fh_->update_record(rid, new_user_record.data, context_);
        }

        updated_ = true;
        return nullptr;
    }

    Rid &rid() override {
        // UpdateExecutor typically doesn't "point" to a single RID after execution in the same way a scan does.
        // _abstract_rid is not explicitly set with a meaningful value for the collection of updates.
        // Returning it as is, or a default.
        return _abstract_rid;
    }

    void beginTuple() override {
        // For UpdateExecutor, beginTuple might not need to do much.
        // Reset the updated_ flag if it were to support multiple executions.
        // updated_ = false; // Reset if re-runnable, but typical UPDATE is one-shot.
    }

    bool is_end() const override {
        return updated_;
    }

    const std::vector<ColMeta>& cols() const override {
        // UpdateExecutor does not produce output columns.
        static const std::vector<ColMeta> empty_cols;
        return empty_cols;
    }

    size_t tupleLen() const override {
        // UpdateExecutor does not produce output tuples.
        return 0;
    }

    std::string getType() override { return "UpdateExecutor"; }
};