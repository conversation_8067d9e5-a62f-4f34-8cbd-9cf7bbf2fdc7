/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "plan_printer.h"
#include <algorithm>
#include <sstream>

std::string PlanPrinter::print_plan(std::shared_ptr<Plan> plan) {
    return print_plan_recursive(plan, 0);
}

std::string PlanPrinter::print_plan(std::shared_ptr<Plan> plan, const std::map<std::string, std::string>& reverse_aliases) {
    return print_plan_recursive(plan, 0, &reverse_aliases);
}

std::string PlanPrinter::print_plan_recursive(std::shared_ptr<Plan> plan, int depth, const std::map<std::string, std::string>* reverse_aliases) {
    if (!plan) return "";
    
    std::string result;
    std::string indent = get_indent(depth);
    
    switch (plan->tag) {
        case T_SeqScan: {
            auto scan_plan = std::dynamic_pointer_cast<ScanPlan>(plan);
            result = indent + "SeqScan(table=" + scan_plan->tab_name_ + ")";
            // 可以选择性地在这里打印 scan_plan->conds_，如果它们直接在扫描层面应用
            break;
        }
        case T_IndexScan: {
            auto scan_plan = std::dynamic_pointer_cast<ScanPlan>(plan);
            result = indent + "IndexScan(table=" + scan_plan->tab_name_;
            if (!scan_plan->index_col_names_.empty()) { // Corrected to index_col_names_
                // 使用已有的 join_strings 辅助函数来格式化列名
                result += ", index_on=[" + join_strings(scan_plan->index_col_names_, ",") + "]"; // Corrected to index_col_names_
            }
            // 同样，可以选择性打印 scan_plan->conds_
            result += ")";
            break;
        }
        case T_Filter: {
            auto filter_plan = std::dynamic_pointer_cast<FilterPlan>(plan);
            result = indent + "Filter(condition=" + format_conditions(filter_plan->conds_, reverse_aliases) + ")";
            if (filter_plan->subplan_) {
                result += "\n" + print_plan_recursive(filter_plan->subplan_, depth + 1, reverse_aliases);
            }
            break;
        }
        case T_Projection: {
            auto proj_plan = std::dynamic_pointer_cast<ProjectionPlan>(plan);
            if (proj_plan->is_select_star_) {
                result = indent + "Project(columns=[*])";
            } else {
                result = indent + "Project(columns=" + format_columns(proj_plan->sel_cols_, reverse_aliases) + ")";
            }
            if (proj_plan->subplan_) {
                result += "\n" + print_plan_recursive(proj_plan->subplan_, depth + 1, reverse_aliases);
            }
            break;
        }
        case T_NestLoop:
        case T_SortMerge: {
            auto join_plan = std::dynamic_pointer_cast<JoinPlan>(plan);
            // 获取所有涉及的表名并排序
            std::vector<std::string> table_names = get_table_names_from_plan(join_plan);
            std::sort(table_names.begin(), table_names.end());
            
            result = indent + "Join(tables=[" + join_strings(table_names, ",") + "]";
            if (!join_plan->conds_.empty()) {
                result += ",condition=" + format_conditions(join_plan->conds_, reverse_aliases);
            }
            result += ")";
            
            // 收集左右子树，按照节点类型和字典序排序
            std::vector<std::shared_ptr<Plan>> child_plans;
            if (join_plan->left_) child_plans.push_back(join_plan->left_);
            if (join_plan->right_) child_plans.push_back(join_plan->right_);
            
            // 按照节点类型和字典序排序
            std::sort(child_plans.begin(), child_plans.end(), [](const std::shared_ptr<Plan>& a, const std::shared_ptr<Plan>& b) {
                return compare_plans(a, b);
            });
            
            // 按排序后的顺序打印子节点
            for (const auto& child : child_plans) {
                result += "\n" + print_plan_recursive(child, depth + 1, reverse_aliases);
            }
            break;
        }
        case T_Sort: {
            auto sort_plan = std::dynamic_pointer_cast<SortPlan>(plan);
            result = indent + "Sort";
            if (sort_plan->subplan_) {
                result += "\n" + print_plan_recursive(sort_plan->subplan_, depth + 1, reverse_aliases);
            }
            break;
        }
        case T_select: {
            // 对于SELECT查询，直接处理其子计划
            auto dml_plan = std::dynamic_pointer_cast<DMLPlan>(plan);
            if (dml_plan && dml_plan->subplan_) {
                result = print_plan_recursive(dml_plan->subplan_, depth, reverse_aliases);
            } else {
                result = indent + "EmptySelectPlan";
            }
            break;
        }
        default:
            result = indent + "UnknownPlan(tag=" + std::to_string(plan->tag) + ")";
            break;
    }
    
    return result;
}

std::string PlanPrinter::format_conditions(const std::vector<Condition>& conds, const std::map<std::string, std::string>* reverse_aliases) {
    std::vector<std::string> cond_strs;
    for (const auto& cond : conds) {
        // 获取左侧表名（优先使用别名）
        std::string lhs_table_name = cond.lhs_col.tab_name;
        if (reverse_aliases) {
            auto it = reverse_aliases->find(cond.lhs_col.tab_name);
            if (it != reverse_aliases->end()) {
                lhs_table_name = it->second;
            }
        }
        
        std::string cond_str = lhs_table_name + "." + cond.lhs_col.col_name;
        cond_str += op_to_string(cond.op);
        
        if (cond.is_rhs_val) {
            cond_str += value_to_string(cond.rhs_val);
        } else {
            // 获取右侧表名（优先使用别名）
            std::string rhs_table_name = cond.rhs_col.tab_name;
            if (reverse_aliases) {
                auto it = reverse_aliases->find(cond.rhs_col.tab_name);
                if (it != reverse_aliases->end()) {
                    rhs_table_name = it->second;
                }
            }
            cond_str += rhs_table_name + "." + cond.rhs_col.col_name;
        }
        cond_strs.push_back(cond_str);
    }
    // 按字典序排序
    std::sort(cond_strs.begin(), cond_strs.end());
    return "[" + join_strings(cond_strs, ",") + "]";
}

std::string PlanPrinter::format_columns(const std::vector<TabCol>& cols, const std::map<std::string, std::string>* reverse_aliases) {
    if (cols.empty()) {
        return "[*]";
    }
    
    std::vector<std::string> col_strs;
    for (const auto& col : cols) {
        if (col.tab_name.empty()) {
            col_strs.push_back(col.col_name);
        } else {
            // 获取表名（优先使用别名）
            std::string table_name = col.tab_name;
            if (reverse_aliases) {
                auto it = reverse_aliases->find(col.tab_name);
                if (it != reverse_aliases->end()) {
                    table_name = it->second;
                }
            }
            col_strs.push_back(table_name + "." + col.col_name);
        }
    }
    // 按字典序排序
    std::sort(col_strs.begin(), col_strs.end());
    return "[" + join_strings(col_strs, ",") + "]";
}

std::string PlanPrinter::get_indent(int depth) {
    std::string indent;
    for (int i = 0; i < depth; i++) {
        indent += "\t";
    }
    return indent;
}

std::string PlanPrinter::op_to_string(CompOp op) {
    switch (op) {
        case OP_EQ: return "=";
        case OP_NE: return "<>";
        case OP_LT: return "<";
        case OP_GT: return ">";
        case OP_LE: return "<=";
        case OP_GE: return ">=";
        default: return "?";
    }
}

std::string PlanPrinter::value_to_string(const Value& val) {
    // common::Value::raw_literal_text 应该存储的是从词法分析器(yytext)直接获取的文本。
    // - 对于字符串，它应该是带引号的，例如 "'Alice'" 或 "'it''s me'"。
    // - 对于数字，它应该是原始数字字符串，例如 "123", "500.45"。
    // - 对于布尔值 (如果通过 analyze.cpp 中的转换设置了 raw_literal_text)，它应该是 "TRUE" 或 "FALSE"。

    if (!val.raw_literal_text.empty()) {
        return val.raw_literal_text;
    }

    // 如果没有原始字面量文本（例如，该 Value 对象不是直接从字面量创建的，
    switch (val.type) {
        case TYPE_INT:
            // 这也可能是布尔值 TRUE/FALSE 被转换为 1/0 后的情况，
            // 但如果 raw_literal_text 为空，我们只能打印数值。
            return std::to_string(val.int_val);
        case TYPE_FLOAT: {
            // 检查是否为整数值的浮点数
            if (val.float_val == static_cast<float>(static_cast<int>(val.float_val))) {
                return std::to_string(static_cast<int>(val.float_val));
            } else {
                // 这对于非字面量浮点数（例如计算结果）或 raw_literal_text 未填充的情况
                std::ostringstream oss;
                oss << val.float_val;
                return oss.str();
            }
        }
        case TYPE_STRING:
            // 理论上，如果 raw_literal_text 为空，但类型是 TYPE_STRING，
            // 这里的 val.str_val 应该是不带引号的内容。
            // 这种情况如果所有字符串字面量都正确填充了 raw_literal_text，则不应发生。
            // 但作为安全回退，我们仍然加上引号。
            return "'" + val.str_val + "'";
        default:
            return "?";
    }
}

std::string PlanPrinter::join_strings(const std::vector<std::string>& strs, const std::string& delimiter) {
    if (strs.empty()) return "";
    
    std::string result = strs[0];
    for (size_t i = 1; i < strs.size(); i++) {
        result += delimiter + strs[i];
    }
    return result;
}

std::vector<std::string> PlanPrinter::get_table_names_from_plan(std::shared_ptr<Plan> plan) {
    std::vector<std::string> table_names;
    
    if (!plan) return table_names;
    
    // 递归收集所有子计划中的表名
    if (auto scan_plan = std::dynamic_pointer_cast<ScanPlan>(plan)) {
        table_names.push_back(scan_plan->tab_name_);
    } else if (auto join_plan = std::dynamic_pointer_cast<JoinPlan>(plan)) {
        auto left_tables = get_table_names_from_plan(join_plan->left_);
        auto right_tables = get_table_names_from_plan(join_plan->right_);
        table_names.insert(table_names.end(), left_tables.begin(), left_tables.end());
        table_names.insert(table_names.end(), right_tables.begin(), right_tables.end());
    } else if (auto filter_plan = std::dynamic_pointer_cast<FilterPlan>(plan)) {
        return get_table_names_from_plan(filter_plan->subplan_);
    } else if (auto proj_plan = std::dynamic_pointer_cast<ProjectionPlan>(plan)) {
        return get_table_names_from_plan(proj_plan->subplan_);
    } else if (auto sort_plan = std::dynamic_pointer_cast<SortPlan>(plan)) {
        return get_table_names_from_plan(sort_plan->subplan_);
    }
    
    return table_names;
}

bool PlanPrinter::compare_plans(const std::shared_ptr<Plan>& a, const std::shared_ptr<Plan>& b) {
    // 首先按照节点类型优先级排序：Filter > Join > Project > Scan
    int priority_a = get_plan_priority(a);
    int priority_b = get_plan_priority(b);
    
    if (priority_a != priority_b) {
        return priority_a < priority_b;
    }
    
    // 同类型节点按照字典序排序
    std::string key_a = get_plan_sort_key(a);
    std::string key_b = get_plan_sort_key(b);
    
    return key_a < key_b;
}

int PlanPrinter::get_plan_priority(const std::shared_ptr<Plan>& plan) {
    if (!plan) return 999;
    
    switch (plan->tag) {
        case T_Filter: return 1;
        case T_NestLoop:
        case T_SortMerge: return 2;
        case T_Projection: return 3;
        case T_SeqScan:
        case T_IndexScan: return 4;
        default: return 5;
    }
}

std::string PlanPrinter::get_plan_sort_key(const std::shared_ptr<Plan>& plan) {
    if (!plan) return "";
    
    switch (plan->tag) {
        case T_SeqScan:
        case T_IndexScan: {
            auto scan_plan = std::dynamic_pointer_cast<ScanPlan>(plan);
            return scan_plan ? scan_plan->tab_name_ : "";
        }
        case T_Filter: {
            auto filter_plan = std::dynamic_pointer_cast<FilterPlan>(plan);
            if (filter_plan && !filter_plan->conds_.empty()) {
                // 使用第一个条件作为排序键
                const auto& cond = filter_plan->conds_[0];
                return cond.lhs_col.tab_name + "." + cond.lhs_col.col_name;
            }
            return "";
        }
        case T_NestLoop:
        case T_SortMerge: {
            auto join_plan = std::dynamic_pointer_cast<JoinPlan>(plan);
            if (join_plan) {
                std::vector<std::string> table_names = get_table_names_from_plan(join_plan);
                std::sort(table_names.begin(), table_names.end());
                return join_strings(table_names, ",");
            }
            return "";
        }
        case T_Projection: {
            auto proj_plan = std::dynamic_pointer_cast<ProjectionPlan>(plan);
            if (proj_plan && !proj_plan->sel_cols_.empty()) {
                // 使用第一个列作为排序键
                const auto& col = proj_plan->sel_cols_[0];
                return col.tab_name + "." + col.col_name;
            }
            return "";
        }
        default:
            return "";
    }
}