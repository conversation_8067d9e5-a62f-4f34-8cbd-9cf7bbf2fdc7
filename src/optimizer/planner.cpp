/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "planner.h"

#include <memory>

#include "execution/executor_delete.h"
#include "execution/executor_index_scan.h"
#include "execution/executor_insert.h"
#include "execution/executor_nestedloop_join.h"
#include "execution/executor_projection.h"
#include "execution/executor_seq_scan.h"
#include "execution/executor_update.h"
#include "index/ix.h"
#include "record_printer.h"
#include "query_optimizer.h"
#include "analyze/analyze.h"
// Anonymous namespace for helper functions local to this file
namespace {

Value planner_convert_sv_value(const std::shared_ptr<ast::Value>& sv_val) {
    Value val;
    if (auto int_lit = std::dynamic_pointer_cast<ast::IntLit>(sv_val)) {
        val.set_int(int_lit->val);
    } else if (auto float_lit = std::dynamic_pointer_cast<ast::FloatLit>(sv_val)) {
        val.set_float(float_lit->val);
    } else if (auto str_lit = std::dynamic_pointer_cast<ast::StringLit>(sv_val)) {
        val.set_str(str_lit->val);
    } else {
        throw InternalError("Planner: Unexpected sv value type during AST condition conversion");
    }
    return val;
}

CompOp planner_convert_sv_comp_op(ast::SvCompOp op) {
    std::map<ast::SvCompOp, CompOp> m = {
        {ast::SV_OP_EQ, OP_EQ}, {ast::SV_OP_NE, OP_NE}, {ast::SV_OP_LT, OP_LT},
        {ast::SV_OP_GT, OP_GT}, {ast::SV_OP_LE, OP_LE}, {ast::SV_OP_GE, OP_GE},
    };
    // Check if op is in map before accessing
    auto it = m.find(op);
    if (it != m.end()) {
        return it->second;
    }
    // It's good to log or indicate which specific operator was unknown.
    throw InternalError("Planner: Unknown SvCompOp (" + std::to_string(static_cast<int>(op)) + ") during AST condition conversion.");
}

void planner_convert_ast_exprs_to_conditions(
    const std::vector<std::shared_ptr<ast::BinaryExpr>>& ast_exprs,
    std::vector<Condition>& conditions,
    SmManager* sm_manager) { // Added SmManager if type/length info is needed for Value::init_raw
    conditions.clear();
    for (const auto& expr : ast_exprs) {
        if (!expr || !expr->lhs || !expr->rhs) {
            throw InternalError("Planner: Invalid AST BinaryExpr (null components) encountered during conversion.");
        }
        Condition cond;
        // Ensure lhs_col is correctly populated. Analyzer should have done this.
        // If tab_name is empty, it might need resolution here or rely on Analyzer.
        cond.lhs_col = {.tab_name = expr->lhs->tab_name, .col_name = expr->lhs->col_name};
        cond.op = planner_convert_sv_comp_op(expr->op);

        if (auto rhs_val_ast = std::dynamic_pointer_cast<ast::Value>(expr->rhs)) {
            cond.is_rhs_val = true;
            cond.rhs_val = planner_convert_sv_value(rhs_val_ast);
            // Potentially, rhs_val might need type adjustment or length info based on lhs_col
            // Example of what Analyzer does (from Analyzer::check_clause):
            // if (cond.is_rhs_val) {
            //     TabMeta &lhs_tab = sm_manager->db_.get_table(cond.lhs_col.tab_name);
            //     auto lhs_col_meta = lhs_tab.get_col(cond.lhs_col.col_name);
            //     cond.rhs_val.init_raw(lhs_col_meta->len); // This ensures raw data buffer is sized
            // }
            // For planner, if analyzer already did this, it's fine. If not, planner might need to.
            // Assuming analyzer has prepared Value objects sufficiently for now.
        } else if (auto rhs_col_ast = std::dynamic_pointer_cast<ast::Col>(expr->rhs)) {
            cond.is_rhs_val = false;
            cond.rhs_col = {.tab_name = rhs_col_ast->tab_name, .col_name = rhs_col_ast->col_name};
        } else {
            throw InternalError("Planner: Unexpected RHS type in AST BinaryExpr during conversion.");
        }
        conditions.push_back(cond);
    }
}

} // end anonymous namespace

// 目前的索引匹配规则为：完全匹配索引字段，且全部为单点查询，不会自动调整where条件的顺序
bool Planner::get_index_cols(std::string &tab_name, std::vector<Condition> &curr_conds,
                             std::vector<std::string> &index_col_names) {
    // index_col_names.clear();
    // for (auto &cond: curr_conds) {
    //     if (cond.is_rhs_val && cond.op == OP_EQ && cond.lhs_col.tab_name == tab_name)
    //         index_col_names.push_back(cond.lhs_col.col_name);
    // }

    if (curr_conds.empty()) {
        return false;
    }

    // TODO
    TabMeta &tab = sm_manager_->db_.get_table(tab_name);
    // TODO 优化：减少索引文件名长度，提高匹配效率
    // conds重复去重

    std::set<std::string> index_set; // 快速查找
    std::unordered_map<std::string, int> conds_map; // 列名 -> Cond
    std::unordered_map<std::string, int> repelicate_conds_map;
    for (std::size_t i = 0; i < curr_conds.size(); ++i) {
        auto &col_name = curr_conds[i].lhs_col.col_name;
        if (index_set.count(col_name) == 0) {
            index_set.emplace(col_name);
            conds_map.emplace(col_name, i);
        } else {
            repelicate_conds_map.emplace(col_name, i);
        }
    }

    int max_len = 0, max_equals = 0, cur_len = 0, cur_equals = 0;
    for (auto &[index_name, index] : tab.indexes) {
        cur_len = cur_equals = 0;
        auto &cols = index.cols;
        for (auto &col : index.cols) {
            if (index_set.count(col.name) == 0) {
                break;
            }
            if (curr_conds[conds_map[col.name]].op == OP_EQ) {
                ++cur_equals;
            }
            ++cur_len;
        }
        // 如果有 where a = 1, b = 1, c > 1;
        // index(a, b, c), index(a, b, c, d);
        // 应该匹配最合适的，避免索引查询中带来的额外拷贝开销
        if (cur_len > max_len && cur_len < curr_conds.size()) {
            // 匹配最长的
            max_len = cur_len;
            index_col_names.clear();
            for (int i = 0; i < index.cols.size(); ++i) {
                index_col_names.emplace_back(index.cols[i].name);
            }
        } else if (cur_len == curr_conds.size()) {
            max_len = cur_len;
            // 最长前缀相等选择等号多的
            if (index_col_names.empty()) {
                for (int i = 0; i < index.cols.size(); ++i) {
                    index_col_names.emplace_back(index.cols[i].name);
                }
                // for (int i = 0; i < cur_len; ++i) {
                //     index_col_names.emplace_back(index.cols[i].name);
                // }
                // = = >  等号优先   = > =     = =
                // = > =           = = > _   = = _
                // } else if(index_col_names.size() > index.cols.size()) {
                //     // 选择最合适的，正好满足，这样减少索引查找的 memcpy
                //     index_col_names.clear();
                //     for (int i = 0; i < index.cols.size(); ++i) {
                //         index_col_names.emplace_back(index.cols[i].name);
                //     }
                // = = >  等号优先   = > =     = =
                // = > =           = = > _   = = _
                // 谁等号多选谁，不管是否合适
            } else if (cur_equals > max_equals) {
                max_equals = cur_equals;
                // cur_len >= cur_equals;
                index_col_names.clear();
                for (int i = 0; i < index.cols.size(); ++i) {
                    index_col_names.emplace_back(index.cols[i].name);
                }
                // for (int i = 0; i < cur_len; ++i) {
                //     index_col_names.emplace_back(index.cols[i].name);
                // }
            }
        }
    }

    // 没有索引
    if (index_col_names.empty()) {
        return false;
    }

    std::vector<Condition> fed_conds; // 理想谓词

    // 连接剩下的非索引列
    // 先清除已经在set中的
    for (auto &index_name : index_col_names) {
        if (index_set.count(index_name)) {
            index_set.erase(index_name);
            fed_conds.emplace_back(std::move(curr_conds[conds_map[index_name]]));
        }
    }

    // 连接 set 中剩下的
    for (auto &index_name : index_set) {
        fed_conds.emplace_back(std::move(curr_conds[conds_map[index_name]]));
    }

    // 连接重复的，如果有
    for (auto &[index_name, idx] : repelicate_conds_map) {
        fed_conds.emplace_back(std::move(curr_conds[repelicate_conds_map[index_name]]));
    }

    curr_conds = std::move(fed_conds);

    // 检查正确与否


    // if (tab.is_index(index_col_names)) return true;
    return true;
}

/**
 * @brief 表算子条件谓词生成
 *
 * @param conds 条件
 * @param tab_names 表名
 * @return std::vector<Condition>
 */
std::vector<Condition> pop_conds(std::vector<Condition> &conds, std::string tab_names) {
    // auto has_tab = [&](const std::string &tab_name) {
    //     return std::find(tab_names.begin(), tab_names.end(), tab_name) != tab_names.end();
    // };
    std::vector<Condition> x_conds_res;
    auto iter = conds.begin();
    while (iter != conds.end()) {
        bool is_rped_x = 0; // default is not sub query
        if (iter->x_rhs_expr.get() != nullptr || iter->x_subquery_res.get() != nullptr) {
            is_rped_x = 1;
            x_conds_res.emplace_back(std::move(*iter));
            iter = conds.erase(iter);
        } else {
            if ((tab_names.compare(iter->lhs_col.tab_name) == 0 && iter->is_rhs_val) || // check the std cond
                (iter->lhs_col.tab_name == iter->rhs_col.tab_name)) {
                is_rped_x = 1;
                x_conds_res.emplace_back(std::move(*iter));
                iter = conds.erase(iter);
            } else ++iter; // iteerator move
        }
        if (!is_rped_x) ++iter;
    }
    return x_conds_res;
}

int push_conds(Condition *cond, std::shared_ptr<Plan> plan)
{
    if(auto x = std::dynamic_pointer_cast<ScanPlan>(plan))
    {
        if(x->tab_name_.compare(cond->lhs_col.tab_name) == 0) {
            return 1;
        } else if(x->tab_name_.compare(cond->rhs_col.tab_name) == 0){
            return 2;
        } else {
            return 0;
        }
    }
    else if(auto x = std::dynamic_pointer_cast<JoinPlan>(plan))
    {
        int left_res = push_conds(cond, x->left_);
        // 条件已经下推到左子节点
        if(left_res == 3){
            return 3;
        }
        int right_res = push_conds(cond, x->right_);
        // 条件已经下推到右子节点
        if(right_res == 3){
            return 3;
        }
        // 左子节点或右子节点有一个没有匹配到条件的列
        if(left_res == 0 || right_res == 0) {
            return left_res + right_res;
        }
        // 左子节点匹配到条件的右边
        if(left_res == 2) {
            // 需要将左右两边的条件变换位置
            std::map<CompOp, CompOp> swap_op = {
                {OP_EQ, OP_EQ}, {OP_NE, OP_NE}, {OP_LT, OP_GT}, {OP_GT, OP_LT}, {OP_LE, OP_GE}, {OP_GE, OP_LE},
            };
            std::swap(cond->lhs_col, cond->rhs_col);
            cond->op = swap_op.at(cond->op);
        }
        x->conds_.emplace_back(std::move(*cond));
        return 3;
    }
    return false;
}

std::shared_ptr<Plan> pop_scan(int *scantbl, std::string table, std::vector<std::string> &joined_tables, 
                std::vector<std::shared_ptr<Plan>> plans)
{
    for (size_t i = 0; i < plans.size(); i++) {
        auto x = std::dynamic_pointer_cast<ScanPlan>(plans[i]);
        if(x->tab_name_.compare(table) == 0)
        {
            scantbl[i] = 1;
            joined_tables.emplace_back(x->tab_name_);
            return plans[i];
        }
    }
    return nullptr;
}


std::shared_ptr<Query> Planner::logical_optimization(std::shared_ptr<Query> query, Context *context)
{
    
    //TODO 实现逻辑优化规则

    return query;
}

std::shared_ptr<Plan> Planner::physical_optimization(std::shared_ptr<Query> query, Context *context)
{
    // 创建查询优化器实例
    QueryOptimizer optimizer(sm_manager_);
    
    // 使用高级优化器进行优化（已包含选择下推）
    std::shared_ptr<Plan> plan = optimizer.optimize(query);
    
    // 投影下推优化
    if (!query->cols.empty()) {
        std::vector<TabCol> required_cols = query->cols;
        plan = optimizer.pushdown_projection(plan, required_cols);
    }
    
    // 顶层添加投影节点（根据任务要求，select语句根节点一定是Project节点）
    std::vector<TabCol> final_cols = query->cols;
    
    // 确保列名包含表名前缀（用于语义分析和执行）
    for (auto& col : final_cols) {
        if (col.tab_name.empty() && !query->tables.empty()) {
            col.tab_name = query->tables[0]; // 单表查询时自动添加表名
        }
    }
    
    // 仅当根节点不是Projection时再加
    if (plan->tag != T_Projection) {
        plan = optimizer.apply_projection(plan, final_cols, query->is_select_star);
    }
    
    // 处理orderby
    plan = generate_sort_plan(query, std::move(plan));

    // 处理limit
    plan = generate_limit_plan(query, std::move(plan));

    return plan;
}



std::shared_ptr<Plan> Planner::make_one_rel(std::shared_ptr<Query> query)
{
    auto select_ast_node = std::dynamic_pointer_cast<ast::SelectStmt>(query->parse);

    // Prefer explicit JOINs from jointree (e.g., SEMI JOIN) if available
    if (select_ast_node && !select_ast_node->jointree.empty()) {
        // Currently, only handling the first join in the tree.
        // A more complex planner might handle a sequence of joins.
        auto& first_join_expr_node = select_ast_node->jointree[0];
        if (first_join_expr_node && first_join_expr_node->type == ::SEMI_JOIN) {
            std::string left_table_name = first_join_expr_node->left;
            std::string right_table_name = first_join_expr_node->right;

            // 1. Create plan for the left table
            // Outer WHERE conditions (query->conds) apply to the left table.
            // Analyzer should have ensured query->conds only refer to the left table.
            // pop_conds extracts conditions for left_table_name from query->conds and modifies query->conds.
            std::vector<Condition> left_table_filters = pop_conds(query->conds, left_table_name);
            
            std::vector<std::string> left_index_col_names;
            bool left_index_exists = get_index_cols(left_table_name, left_table_filters, left_index_col_names);
            std::shared_ptr<Plan> left_plan;
            if (left_index_exists) {
                left_plan = std::make_shared<ScanPlan>(T_IndexScan, sm_manager_, left_table_name, left_table_filters, left_index_col_names);
            } else {
                left_index_col_names.clear();
                left_plan = std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, left_table_name, left_table_filters, left_index_col_names);
            }

            // 2. Create plan for the right table
            // For SEMI JOIN, the right table scan typically doesn't have filters from the outer WHERE clause.
            std::vector<Condition> right_table_filters; // Empty
            std::vector<std::string> right_index_col_names; // Empty, default to SeqScan
            right_index_col_names.clear();
            std::shared_ptr<Plan> right_plan = std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, right_table_name, right_table_filters, right_index_col_names);

            // 3. Convert ON conditions from AST to Planner's Condition struct
            std::vector<Condition> semi_join_on_conditions;
            planner_convert_ast_exprs_to_conditions(first_join_expr_node->conds, semi_join_on_conditions, sm_manager_);

            // 4. Create JoinPlan for SEMI JOIN
            PlanTag join_plan_tag = T_NestLoop; // Default physical join algorithm
            if (enable_nestedloop_join && enable_sortmerge_join) {
                // TODO: Add cost-based decision or a more sophisticated rule for choosing join algorithm
                join_plan_tag = T_NestLoop;
            } else if (enable_nestedloop_join) {
                join_plan_tag = T_NestLoop;
            } else if (enable_sortmerge_join) {
                join_plan_tag = T_SortMerge;
            } else {
                // This case should ideally not be reached if at least one join method is enabled.
                // Or, if knobs are dynamic, it's a valid runtime error.
                throw RMDBError("No join executor selected for SEMI JOIN! Check planner join algorithm enablement.");
            }
            
            auto semi_join_plan = std::make_shared<JoinPlan>(join_plan_tag, left_plan, right_plan, semi_join_on_conditions);
            semi_join_plan->type = ::SEMI_JOIN; // Set the logical join type

            // After processing SEMI JOIN, query->conds should be empty if all filters were for the left table.
            // If query->conds is not empty, it implies conditions that were not filters for the left table scan
            // and are not part of the SEMI JOIN's ON clause. This might indicate a more complex query
            // or an issue with filter pushdown logic. For a simple "SELECT ... FROM L SEMI JOIN R ON ... WHERE L.filter",
            // query->conds should be empty now.
            if (!query->conds.empty()) {
                // This could be an error or require a FilterNode above the semi_join_plan.
                // For now, we assume all relevant filters were pushed or are part of the SEMI JOIN.
                // Consider logging a warning or throwing if strict adherence to simple SEMI JOIN is expected.
                // std::cerr << "Warning: Unhandled conditions remaining after SEMI JOIN planning." << std::endl;
            }
            return semi_join_plan;
        }
    }

    // --- Fallback to existing logic for implicit WHERE joins or single table queries ---
    // The original code starts here.
    // auto x = std::dynamic_pointer_cast<ast::SelectStmt>(query->parse); // Already have select_ast_node from above
    std::vector<std::string> tables = query->tables;
    // // Scan table , 生成表算子列表tab_nodes
    std::vector<std::shared_ptr<Plan>> table_scan_executors(tables.size());
    for (size_t i = 0; i < tables.size(); i++) {
        auto curr_conds = pop_conds(query->conds, tables[i]);
        // int index_no = get_indexNo(tables[i], curr_conds);
        std::vector<std::string> index_col_names;
        bool index_exist = get_index_cols(tables[i], curr_conds, index_col_names);
        if (index_exist == false) {  // 该表没有索引
            index_col_names.clear();
            table_scan_executors[i] = 
                std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, tables[i], curr_conds, index_col_names);
        } else {  // 存在索引
            table_scan_executors[i] =
                std::make_shared<ScanPlan>(T_IndexScan, sm_manager_, tables[i], curr_conds, index_col_names);
        }
    }
    // 只有一个表，不需要join。
    if(tables.size() == 1)
    {
        return table_scan_executors[0];
    }
    // 获取where条件
    auto conds = std::move(query->conds);
    std::shared_ptr<Plan> table_join_executors;
    
    int scantbl[tables.size()];
    for(size_t i = 0; i < tables.size(); i++)
    {
        scantbl[i] = -1;
    }
    // 假设在ast中已经添加了jointree，这里需要修改的逻辑是，先处理jointree，然后再考虑剩下的部分
    if(conds.size() >= 1)
    {
        // 有连接条件

        // 根据连接条件，生成第一层join
        std::vector<std::string> joined_tables(tables.size());
        auto it = conds.begin();
        while (it != conds.end()) {
            std::shared_ptr<Plan> left , right;
            left = pop_scan(scantbl, it->lhs_col.tab_name, joined_tables, table_scan_executors);
            right = pop_scan(scantbl, it->rhs_col.tab_name, joined_tables, table_scan_executors);
            std::vector<Condition> join_conds{*it};
            //建立join
            // 判断使用哪种join方式
            if(enable_nestedloop_join && enable_sortmerge_join) {
                // 默认nested loop join
                table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(left), std::move(right), join_conds);
            } else if(enable_nestedloop_join) {
                table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(left), std::move(right), join_conds);
            } else if(enable_sortmerge_join) {
                table_join_executors = std::make_shared<JoinPlan>(T_SortMerge, std::move(left), std::move(right), join_conds);
            } else {
                // error
                throw RMDBError("No join executor selected!");
            }

            // table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(left), std::move(right), join_conds);
            it = conds.erase(it);
            break;
        }
        // 根据连接条件，生成第2-n层join
        it = conds.begin();
        while (it != conds.end()) {
            std::shared_ptr<Plan> left_need_to_join_executors = nullptr;
            std::shared_ptr<Plan> right_need_to_join_executors = nullptr;
            bool isneedreverse = false;
            if (std::find(joined_tables.begin(), joined_tables.end(), it->lhs_col.tab_name) == joined_tables.end()) {
                left_need_to_join_executors = pop_scan(scantbl, it->lhs_col.tab_name, joined_tables, table_scan_executors);
            }
            if (std::find(joined_tables.begin(), joined_tables.end(), it->rhs_col.tab_name) == joined_tables.end()) {
                right_need_to_join_executors = pop_scan(scantbl, it->rhs_col.tab_name, joined_tables, table_scan_executors);
                isneedreverse = true;
            } 

            if(left_need_to_join_executors != nullptr && right_need_to_join_executors != nullptr) {
                std::vector<Condition> join_conds{*it};
                std::shared_ptr<Plan> temp_join_executors = std::make_shared<JoinPlan>(T_NestLoop, 
                                                                    std::move(left_need_to_join_executors), 
                                                                    std::move(right_need_to_join_executors), 
                                                                    join_conds);
                table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(temp_join_executors), 
                                                                    std::move(table_join_executors), 
                                                                    std::vector<Condition>());
            } else if(left_need_to_join_executors != nullptr || right_need_to_join_executors != nullptr) {
                if(isneedreverse) {
                    std::map<CompOp, CompOp> swap_op = {
                        {OP_EQ, OP_EQ}, {OP_NE, OP_NE}, {OP_LT, OP_GT}, {OP_GT, OP_LT}, {OP_LE, OP_GE}, {OP_GE, OP_LE},
                    };
                    std::swap(it->lhs_col, it->rhs_col);
                    it->op = swap_op.at(it->op);
                    left_need_to_join_executors = std::move(right_need_to_join_executors);
                }
                std::vector<Condition> join_conds{*it};
                table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(left_need_to_join_executors), 
                                                                    std::move(table_join_executors), join_conds);
            } else {
                push_conds(std::move(&(*it)), table_join_executors);
            }
            it = conds.erase(it);
        }
    } else {
        table_join_executors = table_scan_executors[0];
        scantbl[0] = 1;
    }

    //连接剩余表
    for (size_t i = 0; i < tables.size(); i++) {
        if(scantbl[i] == -1) {
            table_join_executors = std::make_shared<JoinPlan>(T_NestLoop, std::move(table_scan_executors[i]), 
                                                    std::move(table_join_executors), std::vector<Condition>());
        }
    }

    return table_join_executors;

}


std::shared_ptr<Plan> Planner::generate_sort_plan(std::shared_ptr<Query> query, std::shared_ptr<Plan> plan)
{
    auto x = std::dynamic_pointer_cast<ast::SelectStmt>(query->parse);
    if(!x->has_sort) {
        return plan;
    }
    std::vector<std::string> tables = query->tables;

    std::vector<ColMeta> all_cols;
    for (auto &sel_tab_name : tables) {
        try {
            const auto &sel_tab_cols = sm_manager_->db_.get_table(sel_tab_name).cols;
            all_cols.insert(all_cols.end(), sel_tab_cols.begin(), sel_tab_cols.end());
        } catch (const std::exception& e) {
            throw;
        }
    }

    // 处理多列排序
    std::vector<std::pair<TabCol, bool>> sort_cols;

    for (const auto& order_item : x->sort_clause->order_items) {
        TabCol sel_col;
        bool found = false;

        for (auto &col : all_cols) {
            if(col.name.compare(order_item.first->col_name) == 0) {
                sel_col = {.tab_name = col.tab_name, .col_name = col.name};
                found = true;
                break;
            }
        }

        bool is_desc = (order_item.second == ast::OrderBy_DESC);
        sort_cols.push_back({sel_col, is_desc});
    }

    auto sort_plan = std::make_shared<SortPlan>(T_Sort, std::move(plan), sort_cols);
    return sort_plan;
}

std::shared_ptr<Plan> Planner::generate_limit_plan(std::shared_ptr<Query> query, std::shared_ptr<Plan> plan)
{
    auto x = std::dynamic_pointer_cast<ast::SelectStmt>(query->parse);

    if (x->limit_count <= 0) {
        return plan;
    }

    auto limit_plan = std::make_shared<LimitPlan>(std::move(plan), x->limit_count);
    return limit_plan;
}

/**
 * @brief select plan 生成
 *
 * @param sel_cols select plan 选取的列
 * @param tab_names select plan 目标的表
 * @param conds select plan 选取条件
 */
std::shared_ptr<Plan> Planner::generate_select_plan(std::shared_ptr<Query> query, Context *context) {
    //逻辑优化
    query = logical_optimization(std::move(query), context);

    //物理优化（已包含顶层projection）
    std::shared_ptr<Plan> plannerRoot = physical_optimization(query, context);

    return plannerRoot;
}

// 生成DDL语句和DML语句的查询执行计划
std::shared_ptr<Plan> Planner::do_planner(std::shared_ptr<Query> query, Context *context)
{
    std::shared_ptr<Plan> plannerRoot;
    std::vector<std::shared_ptr<Plan>> subquery_plans;
    if (auto x = std::dynamic_pointer_cast<ast::CreateTable>(query->parse)) {
        // create table;
        std::vector<ColDef> col_defs;
        for (auto &field : x->fields) {
            if (auto sv_col_def = std::dynamic_pointer_cast<ast::ColDef>(field)) {
                ColDef col_def = {.name = sv_col_def->col_name,
                                  .type = interp_sv_type(sv_col_def->type_len->type),
                                  .len = sv_col_def->type_len->len};
                col_defs.push_back(col_def);
            } else {
                throw InternalError("Unexpected field type");
            }
        }
        plannerRoot = std::make_shared<DDLPlan>(T_CreateTable, x->tab_name, std::vector<std::string>(), col_defs);
    } else if (auto x = std::dynamic_pointer_cast<ast::DropTable>(query->parse)) {
        // drop table;
        plannerRoot = std::make_shared<DDLPlan>(T_DropTable, x->tab_name, std::vector<std::string>(), std::vector<ColDef>());
    } else if (auto x = std::dynamic_pointer_cast<ast::CreateIndex>(query->parse)) {
        // create index;
        plannerRoot = std::make_shared<DDLPlan>(T_CreateIndex, x->tab_name, x->col_names, std::vector<ColDef>());
    } else if (auto x = std::dynamic_pointer_cast<ast::DropIndex>(query->parse)) {
        // drop index
        plannerRoot = std::make_shared<DDLPlan>(T_DropIndex, x->tab_name, x->col_names, std::vector<ColDef>());
    } else if (auto t = std::dynamic_pointer_cast<ast::ShowIndex>(query->parse)) {
        //show index
        plannerRoot =std::make_shared<DDLPlan>(T_ShowIndex, t->tab_name, std::vector<std::string>(), std::vector<ColDef>());
    } else if (auto x = std::dynamic_pointer_cast<ast::InsertStmt>(query->parse)) {
        // insert;
        plannerRoot = std::make_shared<DMLPlan>(T_Insert, std::shared_ptr<Plan>(),  x->tab_name,  
                                                    query->values, std::vector<Condition>(), std::vector<SetClause>());
    } else if (auto x = std::dynamic_pointer_cast<ast::DeleteStmt>(query->parse)) {
        // delete;
        // 生成表扫描方式
        std::shared_ptr<Plan> table_scan_executors;
        // 只有一张表，不需要进行物理优化了
        // int index_no = get_indexNo(x->tab_name, query->conds);
        std::vector<std::string> index_col_names;
        bool index_exist = get_index_cols(x->tab_name, query->conds, index_col_names);
        
        if (index_exist == false) {  // 该表没有索引
            index_col_names.clear();
            table_scan_executors = 
                std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, x->tab_name, query->conds, index_col_names);
        } else {  // 存在索引
            table_scan_executors =
                std::make_shared<ScanPlan>(T_IndexScan, sm_manager_, x->tab_name, query->conds, index_col_names);
        }

        plannerRoot = std::make_shared<DMLPlan>(T_Delete, table_scan_executors, x->tab_name,  
                                                std::vector<Value>(), query->conds, std::vector<SetClause>());
    } else if (auto x = std::dynamic_pointer_cast<ast::UpdateStmt>(query->parse)) {
        // update;
        // 生成表扫描方式
        std::shared_ptr<Plan> table_scan_executors;
        // 只有一张表，不需要进行物理优化了
        // int index_no = get_indexNo(x->tab_name, query->conds);
        std::vector<std::string> index_col_names;
        bool index_exist = get_index_cols(x->tab_name, query->conds, index_col_names);

        if (index_exist == false) {  // 该表没有索引
        index_col_names.clear();
            table_scan_executors = 
                std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, x->tab_name, query->conds, index_col_names);
        } else {  // 存在索引
            table_scan_executors =
                std::make_shared<ScanPlan>(T_IndexScan, sm_manager_, x->tab_name, query->conds, index_col_names);
        }
        plannerRoot = std::make_shared<DMLPlan>(T_Update, table_scan_executors, x->tab_name,
                                                     std::vector<Value>(), query->conds, 
                                                     query->set_clauses);
    } else if (auto _x = std::dynamic_pointer_cast<ast::SelectStmt>(query->parse)) {
        std::vector<Condition> x_joi_conds;
        std::vector<Condition> x_scan_conds;
        std::vector<TabCol> x_proj_cols = query->cols;
        for (auto &cond_i: query->conds) { // handle no-rela sub query
            if (cond_i.x_rhs_expr) {
                x_joi_conds.push_back(cond_i);
                subquery_plans.push_back(do_planner(cond_i.x_subquery_res, context));
                query->cols.push_back(cond_i.lhs_col);
            } else x_scan_conds.push_back(cond_i);
        }
        query->conds = std::move(x_scan_conds); // gcc help
        std::shared_ptr<plannerInfo> x_cond_rt = std::make_shared<plannerInfo>(_x);
        if (_x->contains_agg) {
            std::shared_ptr<Plan> table_scan_executors;
            std::vector<std::string> index_col_names;
            bool index_exist = get_index_cols(_x->agg_tab_name, query->conds, index_col_names);
            if (!index_exist) { // is err, has no index
                index_col_names.clear();
                table_scan_executors = std::make_shared<ScanPlan>(T_SeqScan, sm_manager_, _x->agg_tab_name, query->conds, index_col_names);
            } else { // has index, is right
                auto index_scan = std::make_shared<ScanPlan>(T_IndexScan, sm_manager_, _x->agg_tab_name, query->conds, index_col_names);
                TabMeta &x_tab_i = sm_manager_->db_.get_table(_x->agg_tab_name);
                index_scan->index_meta_ = x_tab_i.get_index_meta(index_col_names);
                table_scan_executors = index_scan;
            }
            auto x_aggr_plan = std::make_shared<DMLPlan>(T_Aggregation, table_scan_executors, _x->agg_tab_name,
                                                             std::vector<Value>(), query->conds,
                                                             std::vector<SetClause>());
            x_aggr_plan->cols_ = _x->cols;
            x_aggr_plan->x_aggregations_ = query->agg_meta;
            x_aggr_plan->outp_x_cols_ = query->cols;
            x_aggr_plan->collect_group_by_ = _x->group_clause_;
            plannerRoot = x_aggr_plan;
        } else {
            std::shared_ptr<Plan> projection = generate_select_plan(std::move(query), context);
          
            plannerRoot = std::make_shared<DMLPlan>(T_select, projection, std::string(), std::vector<Value>(),
                                                    std::vector<Condition>(), std::vector<SetClause>());
          
        }
        for(auto subquery_plan : subquery_plans) {
   
            plannerRoot = std::make_shared<JoinPlan>(T_NestLoop, plannerRoot, subquery_plan, x_joi_conds);
          
            plannerRoot = std::make_shared<ProjectionPlan>(T_Projection, plannerRoot, x_proj_cols);

            plannerRoot = std::make_shared<DMLPlan>(T_sub_select, plannerRoot, std::string(), std::vector<Value>(),
                                                    std::vector<Condition>(), std::vector<SetClause>());
        }
    } else {
        throw InternalError("Unexpected AST root");
    }
    return plannerRoot;
}