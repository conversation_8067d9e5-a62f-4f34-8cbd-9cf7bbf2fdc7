/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include "plan.h"
#include "common/common.h"

class PlanPrinter {
public:
    static std::string print_plan(std::shared_ptr<Plan> plan);
    static std::string print_plan(std::shared_ptr<Plan> plan, const std::map<std::string, std::string>& reverse_aliases);
    
private:
    static std::string print_plan_recursive(std::shared_ptr<Plan> plan, int depth, const std::map<std::string, std::string>* reverse_aliases = nullptr);
    static std::string format_conditions(const std::vector<Condition>& conds, const std::map<std::string, std::string>* reverse_aliases = nullptr);
    static std::string format_columns(const std::vector<TabCol>& cols, const std::map<std::string, std::string>* reverse_aliases = nullptr);
    static std::string get_indent(int depth);
    static std::string op_to_string(CompOp op);
    static std::string value_to_string(const Value& val);
    static std::string join_strings(const std::vector<std::string>& strs, const std::string& delimiter);
    static std::vector<std::string> get_table_names_from_plan(std::shared_ptr<Plan> plan);
    static bool compare_plans(const std::shared_ptr<Plan>& a, const std::shared_ptr<Plan>& b);
    static int get_plan_priority(const std::shared_ptr<Plan>& plan);
    static std::string get_plan_sort_key(const std::shared_ptr<Plan>& plan);
};