/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <set>
#include "plan.h"
#include "analyze/analyze.h"
#include "system/sm.h"

class QueryOptimizer {
private:
    SmManager* sm_manager_;
    
public:
    QueryOptimizer(SmManager* sm_manager) : sm_manager_(sm_manager) {}
    
    // 主要优化入口
    std::shared_ptr<Plan> optimize(std::shared_ptr<Query> query);
    
    // 选择运算下推
    std::shared_ptr<Plan> pushdown_selection(std::shared_ptr<Plan> plan, std::vector<Condition>& conds);
    
    // 投影运算下推  
    std::shared_ptr<Plan> pushdown_projection(std::shared_ptr<Plan> plan, std::vector<TabCol>& required_cols);
    
    // 连接顺序优化
    std::shared_ptr<Plan> optimize_join_order(const std::vector<std::string>& tables, std::vector<Condition>& join_conds);
    
    // 获取表的基数（行数）
    size_t get_table_cardinality(const std::string& table_name);
    
    // 估算连接结果的基数
    size_t estimate_join_cardinality(const std::string& left_table, const std::string& right_table);
    
    // 生成基本的扫描计划
    std::shared_ptr<Plan> generate_scan_plan(const std::string& table_name, const std::vector<Condition>& conds);
    
    // 应用选择条件到计划
    std::shared_ptr<Plan> apply_selection(std::shared_ptr<Plan> plan, const std::vector<Condition>& conds);
    
    // 应用投影到计划
    std::shared_ptr<Plan> apply_projection(std::shared_ptr<Plan> plan, const std::vector<TabCol>& cols, bool is_select_star = false);
    
    // 辅助方法
    std::set<std::string> get_plan_table_names(std::shared_ptr<Plan> plan);
    bool needs_projection(std::shared_ptr<Plan> plan, const std::vector<TabCol>& required_cols);

};