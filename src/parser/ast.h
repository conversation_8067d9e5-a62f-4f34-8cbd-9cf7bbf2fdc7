/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */
#pragma once

#include <vector>
#include <string>
#include <memory>
#include <map>

#include <optional>
#include <iostream>
enum JoinType {
    INNER_JOIN, LEFT_JOIN, RIGHT_JOIN, FULL_JOIN, SEMI_JOIN
};
namespace ast {

enum SvType {
    SV_TYPE_INT, SV_TYPE_FLOAT, SV_TYPE_STRING, SV_TYPE_BOOL
};

enum SvCompOp {
    SV_OP_EQ, SV_OP_NE, SV_OP_LT, SV_OP_GT, SV_OP_LE, SV_OP_GE, SV_OP_IN
};

// 添加算术运算符枚举
enum SvArithOp {
    SV_OP_ADD, SV_OP_SUB, SV_OP_MUL, SV_OP_DIV
};

enum OrderByDir {
    OrderBy_DEFAULT,
    OrderBy_ASC,
    OrderBy_DESC
};

enum SvAggregationType {
    SV_AGGRE_COUNT,
    SV_AGGRE_MAX,
    SV_AGGRE_MIN,
    SV_AGGRE_SUM,
    SV_AGGRE_AVG
};

enum SetKnobType {
    EnableNestLoop, EnableSortMerge
};

// Base class for tree nodes
struct TreeNode {
    virtual ~TreeNode() = default;  // enable polymorphism
};

struct Help : public TreeNode {
};

struct ShowTables : public TreeNode {
};

struct TxnBegin : public TreeNode {
};

struct TxnCommit : public TreeNode {
};

struct TxnAbort : public TreeNode {
};

struct TxnRollback : public TreeNode {
};

struct TypeLen : public TreeNode {
    SvType type;
    int len;

    TypeLen(SvType type_, int len_) : type(type_), len(len_) {}
};

struct Field : public TreeNode {
};

struct ColDef : public Field {
    std::string col_name;
    std::shared_ptr<TypeLen> type_len;

    ColDef(std::string col_name_, std::shared_ptr<TypeLen> type_len_) :
            col_name(std::move(col_name_)), type_len(std::move(type_len_)) {}
};

struct CreateTable : public TreeNode {
    std::string tab_name;
    std::vector<std::shared_ptr<Field>> fields;

    CreateTable(std::string tab_name_, std::vector<std::shared_ptr<Field>> fields_) :
            tab_name(std::move(tab_name_)), fields(std::move(fields_)) {}
};

struct DropTable : public TreeNode {
    std::string tab_name;

    DropTable(std::string tab_name_) : tab_name(std::move(tab_name_)) {}
};

struct DescTable : public TreeNode {
    std::string tab_name;

    DescTable(std::string tab_name_) : tab_name(std::move(tab_name_)) {}
};

struct CreateIndex : public TreeNode {
    std::string tab_name;
    std::vector<std::string> col_names;

    CreateIndex(std::string tab_name_, std::vector<std::string> col_names_) :
            tab_name(std::move(tab_name_)), col_names(std::move(col_names_)) {}
};

struct DropIndex : public TreeNode {
    std::string tab_name;
    std::vector<std::string> col_names;

    DropIndex(std::string tab_name_, std::vector<std::string> col_names_) :
            tab_name(std::move(tab_name_)), col_names(std::move(col_names_)) {}
};

struct ShowIndex : public TreeNode
{
    std::string tab_name;
    ShowIndex(std::string tab_name_) : tab_name(std::move(tab_name_)) {}
};

struct Expr : public TreeNode {
};

struct Value : public Expr {
};

struct IntLit : public Value {
    int val;
    std::string raw_text;

    IntLit(int val_, std::string raw_text_) : val(val_), raw_text(std::move(raw_text_)) {}
};

struct FloatLit : public Value {
    float val;
    std::string raw_text;

    FloatLit(float val_, std::string raw_text_) : val(val_), raw_text(std::move(raw_text_)) {}
};

struct StringLit : public Value {
    std::string val; // Stores the string content without quotes
    std::string raw_text; // Stores the original literal, e.g., "'Alice'" or "'it''s me'"

    StringLit(std::string val_, std::string raw_text_) : val(std::move(val_)), raw_text(std::move(raw_text_)) {}
};

struct BoolLit : public Value {
    bool val;

    BoolLit(bool val_) : val(val_) {}
};

struct Col : public Expr {
    std::string tab_name;
    std::string col_name;
    std::optional<SvAggregationType> ag_type;
    std::optional<std::string> as_name;
    bool is_aggregation = false;
    // 构造函数用于普通列
    Col(std::string tab_name_, std::string col_name_)
        : tab_name(std::move(tab_name_)), col_name(std::move(col_name_)), ag_type(std::nullopt),
            as_name(std::nullopt) {
    }

    // 构造函数用于聚合列
    Col(std::string tab_name_, std::string col_name_, SvAggregationType ag_type_, std::string as_name_)
        : tab_name(std::move(tab_name_)), col_name(std::move(col_name_)), ag_type(ag_type_),
            as_name(std::move(as_name_)) {
        if (ag_type.has_value()) is_aggregation = true;
        if (!as_name.has_value() || as_name.value().empty()) {
            std::string ag_type_name;
            if (ag_type.value() == SV_AGGRE_COUNT) {
                ag_type_name = "COUNT";
            } else if (ag_type.value() == SV_AGGRE_SUM) {
                ag_type_name = "SUM";
            } else if (ag_type.value() == SV_AGGRE_MIN) {
                ag_type_name = "MIN";
            } else if (ag_type.value() == SV_AGGRE_MAX) {
                ag_type_name = "MAX";
            } else if (ag_type.value() == SV_AGGRE_AVG) {
                ag_type_name = "AVG";
            }
            if (col_name.empty()) {
                as_name = ag_type_name + "(*)";
            } else {
                as_name = ag_type_name + "(" + col_name + ")";
            }
        }
    }

};

struct SetClause : public TreeNode {
    std::string col_name;
    std::shared_ptr<Expr> expr;  // 改为支持表达式

    SetClause(std::string col_name_, std::shared_ptr<Expr> expr_) :
            col_name(std::move(col_name_)), expr(std::move(expr_)) {}
};

struct BinaryExpr : public TreeNode {
    std::shared_ptr<Col> lhs;
    SvCompOp op;
    std::shared_ptr<Expr> rhs;
    std::vector<std::shared_ptr<Value>> in_values;
    BinaryExpr(std::shared_ptr<Col> lhs_, SvCompOp op_, std::shared_ptr<Expr> rhs_) :
            lhs(std::move(lhs_)), op(op_), rhs(std::move(rhs_)) {}
    BinaryExpr(std::shared_ptr<Col> lhs_, std::vector<std::shared_ptr<Value>> in_values_) : lhs(std::move(lhs_)),
        in_values(std::move(in_values_)) {
        op = SV_OP_IN;
    }

    BinaryExpr(std::shared_ptr<Col> lhs_, std::shared_ptr<Expr> rhs_) : lhs(std::move(lhs_)),
        rhs(std::move(rhs_)) {
        op = SV_OP_IN;
    }
};

// 添加算术表达式节点
struct ArithmeticExpr : public Expr {
    std::shared_ptr<Expr> lhs;
    SvArithOp op;
    std::shared_ptr<Expr> rhs;

    ArithmeticExpr(std::shared_ptr<Expr> lhs_, SvArithOp op_, std::shared_ptr<Expr> rhs_) :
            lhs(std::move(lhs_)), op(op_), rhs(std::move(rhs_)) {}
};

struct OrderBy : public TreeNode
{
    std::vector<std::pair<std::shared_ptr<Col>, OrderByDir>> order_items;

    // 新的构造函数，支持多列排序
    OrderBy(std::vector<std::pair<std::shared_ptr<Col>, OrderByDir>> order_items_) :
       order_items(std::move(order_items_)) {}

    // 为了向后兼容，保留单列构造函数
    OrderBy(std::shared_ptr<Col> cols_, OrderByDir orderby_dir_) :
       order_items({{std::move(cols_), orderby_dir_}}) {}
};

struct InsertStmt : public TreeNode {
    std::string tab_name;
    std::vector<std::shared_ptr<Value>> vals;

    InsertStmt(std::string tab_name_, std::vector<std::shared_ptr<Value>> vals_) :
            tab_name(std::move(tab_name_)), vals(std::move(vals_)) {}
};

struct DeleteStmt : public TreeNode {
    std::string tab_name;
    std::vector<std::shared_ptr<BinaryExpr>> conds;

    DeleteStmt(std::string tab_name_, std::vector<std::shared_ptr<BinaryExpr>> conds_) :
            tab_name(std::move(tab_name_)), conds(std::move(conds_)) {}
};

struct UpdateStmt : public TreeNode {
    std::string tab_name;
    std::vector<std::shared_ptr<SetClause>> set_clauses;
    std::vector<std::shared_ptr<BinaryExpr>> conds;

    UpdateStmt(std::string tab_name_,
               std::vector<std::shared_ptr<SetClause>> set_clauses_,
               std::vector<std::shared_ptr<BinaryExpr>> conds_) :
            tab_name(std::move(tab_name_)), set_clauses(std::move(set_clauses_)), conds(std::move(conds_)) {}
};

struct JoinExpr : public TreeNode {
    std::string left;
    std::string right;
    std::vector<std::shared_ptr<BinaryExpr>> conds;
    JoinType type;

    JoinExpr(std::string left_, std::string right_,
               std::vector<std::shared_ptr<BinaryExpr>> conds_, JoinType type_) :
            left(std::move(left_)), right(std::move(right_)), conds(std::move(conds_)), type(type_) {}
};

struct GroupBy : public TreeNode {
    std::vector<std::string> group_by_name_;
    std::vector<std::shared_ptr<BinaryExpr> > having_conds_;
    bool is_group_by;
    bool is_having_conds;

    GroupBy(std::vector<std::string> group_by_name,
            std::vector<std::shared_ptr<BinaryExpr> > having_conds) : group_by_name_(std::move(group_by_name)),
                                                                        having_conds_(std::move(having_conds)) {
        if (!group_by_name_.empty()) {
            is_group_by = true;
        }
        if (!having_conds_.empty()) {
            is_having_conds = true;
        }
    }
};

struct SelectStmt : public TreeNode {
    std::vector<std::shared_ptr<Col>> cols;
    std::vector<std::string> tabs;
    std::vector<std::shared_ptr<BinaryExpr>> conds;
    std::vector<std::shared_ptr<JoinExpr>> jointree;
    std::shared_ptr<OrderBy> sort_clause;
    std::shared_ptr<GroupBy> group_clause_;
    // 表别名映射：alias -> table_name
    std::map<std::string, std::string> table_aliases;

    bool has_sort;
    bool contains_agg = false;
    bool contains_having = false;
    bool contains_groupby = false;
    int limit_count = -1;  // -1表示没有LIMIT，否则表示LIMIT的数量

    std::string agg_tab_name;
    
    SelectStmt(std::vector<std::shared_ptr<Col> > cols_,
                std::vector<std::string> tabs_,
                std::vector<std::shared_ptr<BinaryExpr> > conds_,
                std::shared_ptr<OrderBy> order_,
                std::shared_ptr<GroupBy> collect_group_by) :
            cols(std::move(cols_)), tabs(std::move(tabs_)), conds(std::move(conds_)),
            sort_clause(std::move(order_)),group_clause_(std::move(collect_group_by)) {
                has_sort = (bool)sort_clause;
                contains_groupby = group_clause_ != nullptr && !group_clause_->group_by_name_.empty();
                contains_having = group_clause_ != nullptr && !group_clause_->having_conds_.empty();
                for (const auto &pd: cols) {
                    if (contains_agg == false && pd->is_aggregation == true) {
                        contains_agg = true;
                        agg_tab_name = tabs[0];
                    }
                }
            }
};

// EXPLAIN statement
struct ExplainStmt : public TreeNode {
    std::shared_ptr<TreeNode> stmt;  // 被解释的语句
    
    ExplainStmt(std::shared_ptr<TreeNode> stmt_) : stmt(std::move(stmt_)) {}
};

// set enable_nestloop
struct SetStmt : public TreeNode {
    SetKnobType set_knob_type_;
    bool bool_val_;

    SetStmt(SetKnobType &type, bool bool_value) :
        set_knob_type_(type), bool_val_(bool_value) { }
};

struct ScalarSubqueryExpr : public Expr {
    std::shared_ptr<SelectStmt> subquery;

    ScalarSubqueryExpr(std::shared_ptr<SelectStmt> subquery_)
        : subquery(std::move(subquery_)) {}
};

// Semantic value
struct SemValue {
    int sv_int;
    float sv_float;
    std::string sv_str;
    bool sv_bool;
    OrderByDir sv_orderby_dir;
    std::vector<std::string> sv_strs;
    std::string sv_raw_text; // For storing raw text of literals from lexer

    std::shared_ptr<TreeNode> sv_node;

    SvCompOp sv_comp_op;

    std::shared_ptr<TypeLen> sv_type_len;

    std::shared_ptr<Field> sv_field;
    std::vector<std::shared_ptr<Field>> sv_fields;

    std::shared_ptr<Expr> sv_expr;

    std::shared_ptr<Value> sv_val;
    std::vector<std::shared_ptr<Value>> sv_vals;

    std::shared_ptr<Col> sv_col;
    std::vector<std::shared_ptr<Col>> sv_cols;

    std::shared_ptr<SetClause> sv_set_clause;
    std::vector<std::shared_ptr<SetClause>> sv_set_clauses;

    std::shared_ptr<BinaryExpr> sv_cond;
    std::vector<std::shared_ptr<BinaryExpr>> sv_conds;

    std::shared_ptr<OrderBy> sv_sort_by;
    std::vector<std::pair<std::shared_ptr<Col>, OrderByDir>> sv_order_items;
    std::pair<std::shared_ptr<Col>, OrderByDir> sv_order_item;

    SetKnobType sv_setKnobType;
    JoinType sv_join_type;

    SvAggregationType sv_ag_type;
    std::shared_ptr<GroupBy> sv_grp_by;
    
    SvArithOp sv_arith_op;  // 添加算术运算符类型
};

extern std::shared_ptr<ast::TreeNode> parse_tree;

}

#define YYSTYPE ast::SemValue
