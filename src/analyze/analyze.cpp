/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "analyze.h"
#include <iterator>
#include <ostream>
#include <string>

// 移除全局变量，改用参数传递方式
 #define GPPSTR std::string
/**
 * @description: 语义分析器主函数，对解析树进行语义分析和查询重写
 * @param {shared_ptr<ast::TreeNode>} parse 解析器生成的抽象语法树
 * @return {shared_ptr<Query>} 分析后的查询对象
 * @note: 检查表存在性、列引用合法性、类型兼容性等语义规则
 */
std::shared_ptr<Query> Analyze::do_analyze(std::shared_ptr<ast::TreeNode> parse)
{
    std::shared_ptr<Query> query = std::make_shared<Query>();
    if (auto x = std::dynamic_pointer_cast<ast::SelectStmt>(parse))
    {
            // 处理 SEMI JOIN 的情况
            std::string semi_join_left_table_name;   // SEMI JOIN 左表名
            std::string semi_join_right_table_name;  // SEMI JOIN 右表名
            ast::JoinExpr* semi_join_expr = nullptr; // SEMI JOIN 表达式指针

            // 处理JOIN表达式
            ast::JoinExpr* join_expr = nullptr;
            
            // 检查是否是group_by——SELECT 列表中不能出现没有在 GROUP BY ⼦句中的⾮聚集列
            if (!x->jointree.empty()) {
                auto& first_join_expr_node = x->jointree[0];
                if (first_join_expr_node) {
                    join_expr = first_join_expr_node.get();
                    
                    if (first_join_expr_node->type == ::SEMI_JOIN) {
                        semi_join_expr = first_join_expr_node.get();
                        semi_join_left_table_name = semi_join_expr->left;
                        semi_join_right_table_name = semi_join_expr->right;

                        // 验证左表名一致性（解析器规则将左表放在 x->tabs 中）
                        if (x->tabs.empty() || x->tabs[0] != semi_join_left_table_name) {
                            throw InternalError("SEMI JOIN 左表名在解析器和分析器之间不一致");
                        }
                    }
                    // INNER JOIN等其他类型的JOIN在这里处理
                    // 连接条件会在后续的优化阶段使用
                }
            }

            // 设置查询涉及的表名列表
            query->tables = std::move(x->tabs);
            
            // 复制别名映射
            query->table_aliases = x->table_aliases;
            
            // 构建反向别名映射（table_name -> alias）
            for (const auto& pair : query->table_aliases) {
                query->reverse_table_aliases[pair.second] = pair.first;
            }
            
            // 设置当前别名映射，供check_column等函数使用
            current_query_aliases_ = &(query->table_aliases);
            
            // 统一检查所有涉及表的存在性（包括 SEMI JOIN 的右表）
            // 首先收集所有可能包含别名的表引用
            std::vector<std::string> tables_or_aliases_to_check = query->tables;
            if (semi_join_expr) {
                // SEMI JOIN 的右表不在 query->tables 中，需要单独检查
                tables_or_aliases_to_check.push_back(semi_join_right_table_name);
            }

            // 解析别名，得到需要检查的真实表名列表
            std::vector<std::string> real_tables_to_check;
            for (const auto& name_or_alias : tables_or_aliases_to_check) {
                auto it = query->table_aliases.find(name_or_alias);
                if (it != query->table_aliases.end()) {
                    real_tables_to_check.push_back(it->second); // 是别名，使用真实表名
                } else {
                    real_tables_to_check.push_back(name_or_alias); // 不是别名（或已经是真实表名），直接使用
                }
            }
            
            for (const auto& tab_name : real_tables_to_check) {
                if (!sm_manager_->db_.is_table(tab_name)) {
                    throw TableNotFoundError(tab_name);
                }
            }

            // 确定列检查和条件检查的上下文表名列表
            std::vector<std::string> select_context_tables;  // SELECT 子句可见的表名
            std::vector<std::string> where_context_tables;   // WHERE 子句可见的表名

            if (semi_join_expr) {
                // SEMI JOIN：只有左表对 SELECT 和 WHERE 可见
                std::string real_left_name = semi_join_left_table_name;
                auto it_left = query->table_aliases.find(semi_join_left_table_name);
                if (it_left != query->table_aliases.end()) {
                    real_left_name = it_left->second;
                }
                select_context_tables = {real_left_name};
                where_context_tables = {real_left_name};
            } else {
                // 普通查询：所有 FROM 子句中的表都可见
                // 解析 query->tables 到真实表名
                for (const auto& name_or_alias : query->tables) {
                    auto it = query->table_aliases.find(name_or_alias);
                    if (it != query->table_aliases.end()) {
                        select_context_tables.push_back(it->second);
                        where_context_tables.push_back(it->second);
                    } else {
                        select_context_tables.push_back(name_or_alias);
                        where_context_tables.push_back(name_or_alias);
                    }
                }
            }
            
            // 获取 SELECT 子句可用的所有列信息
            std::vector<ColMeta> available_cols_for_select;
            get_all_cols(select_context_tables, available_cols_for_select);

            // 处理target list (SELECT clause)
            // Clear query->cols before populating to ensure a clean state.
            query->cols.clear();

            if (x->cols.empty()) { // SELECT *
                query->is_select_star = true;
                for (auto &col_meta : available_cols_for_select) {
                    query->cols.push_back({.tab_name = col_meta.tab_name, .col_name = col_meta.name});
                }

                // 检查 SELECT * 与 GROUP BY 的兼容性
                if (x->group_clause_ && !x->group_clause_->group_by_name_.empty()) {
                    // 当使用 GROUP BY 时，SELECT * 是不允许的，因为它包含了非分组列
                    std::cerr << "analyze::Error: SELECT * cannot be used with GROUP BY clause" << std::endl;
                    throw GroupByFuncError("analyze::Error: SELECT * cannot be used with GROUP BY clause");
                }
            } else { // 指定了具体的列
                query->is_select_star = false;
                // 一次性处理：初始化并验证列引用
                bool only_one_table_in_context = (select_context_tables.size() == 1);
                std::string single_table_name_if_any = only_one_table_in_context ? select_context_tables[0] : "";

                for (auto &sv_sel_col : x->cols) {
                    TabCol sel_col;

                    if (sv_sel_col->is_aggregation) {
                        // 对于聚集函数，使用别名作为输出列名
                        sel_col = {.tab_name = sv_sel_col->tab_name, .col_name = sv_sel_col->as_name.value()};
                    } else {
                        // 对于普通列，使用原始列名
                        sel_col = {.tab_name = sv_sel_col->tab_name, .col_name = sv_sel_col->col_name};

                        // 如果 SELECT 上下文中只有一个表，且列没有指定表名，则自动分配该表名
                        if (only_one_table_in_context && sel_col.tab_name.empty()) {
                            sel_col.tab_name = single_table_name_if_any;
                        }

                        // 验证并推断表名（如果未指定），使用适当的上下文
                        sel_col = check_column(available_cols_for_select, select_context_tables, sel_col);
                    }

                    query->cols.push_back(sel_col);
                }
            }

            // GROUP BY 和聚集函数验证
            if (x->group_clause_ && !x->group_clause_->group_by_name_.empty()) {
                // 情况1：有 GROUP BY 子句
                // SELECT 列表中的非聚集列必须在 GROUP BY 子句中出现
                for (const auto &col: x->cols) {
                    if (!col->is_aggregation) {
                        bool in_groupby = false;
                        for (const auto &group_col: x->group_clause_->group_by_name_) {
                            if (col->col_name == group_col) {
                                in_groupby = true;
                                break;
                            }
                        }
                        if (!in_groupby) {
                            std::cerr << "analyze::Error: Column '" + col->col_name + "' is not in GROUP BY clause" << std::endl;
                            throw GroupByFuncError("analyze::Error: Column '" + col->col_name + "' is not in GROUP BY clause");
                        }
                    }
                }
            } else if (x->contains_agg) {
                // 情况2：没有 GROUP BY 但有聚集函数
                // 不能混合聚合和非聚合列
                bool has_agg = false;
                bool has_non_agg = false;
                for (const auto &col: x->cols) {
                    if (col->is_aggregation) {
                        has_agg = true;
                    } else {
                        has_non_agg = true;
                    }
                }
                if (has_agg && has_non_agg) {
                    std::cerr << "analyze::Error: Cannot mix aggregate and non-aggregate columns without GROUP BY" << std::endl;
                    throw GroupByFuncError("analyze::Error: Cannot mix aggregate and non-aggregate columns without GROUP BY clause");
                }
            }
            // 情况3：没有 GROUP BY 也没有聚集函数 - 正常处理，无需特殊验证

            // 处理聚集函数元数据
            if (x->contains_agg) {
                for (const auto &col: x->cols) {
                    if (col->is_aggregation) {
                        // 对于 COUNT(*)，col_name 是空字符串，需要特殊处理
                        std::string target_col_name = col->col_name;
                        if (col->ag_type == AG_COUNT && target_col_name.empty()) {
                            target_col_name = "*";  // 使用 "*" 作为 COUNT(*) 的标识符
                        }
                        TabCol selCol = {.tab_name = select_context_tables.empty() ? "" : select_context_tables[0], .col_name = target_col_name};
                        query->agg_meta.push_back({
                            .op = convert_sv_to_ag(col->ag_type.value()), .target_col = selCol
                        });
                        // SUM 函数类型检查（跳过 COUNT(*) 的情况）
                        if (col->ag_type == AG_SUM && col->col_name != "*") {
                            // 需要找到对应的表来检查列类型
                            for (const auto& table_name : select_context_tables) {
                                auto tableMeta = sm_manager_->db_.get_table(table_name);
                                if (tableMeta.is_col(col->col_name)) {
                                    auto check_col = tableMeta.get_col(col->col_name);
                                    if (check_col->type == TYPE_STRING) throw OutputNotSuitError();
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            // WHERE 子句中不能用聚集函数作为条件表达式
            for (auto con: x->conds) {
                // 检查左侧是否有聚集函数
                if (con->lhs->is_aggregation) {
                    std::cerr << "analyze::Error: Aggregate functions are not allowed in WHERE clause" << std::endl;
                    throw GroupByFuncError("analyze::Error: Aggregate functions are not allowed in WHERE clause");
                }
                // 检查右侧是否有聚集函数
                if (auto rhs_col = std::dynamic_pointer_cast<ast::Col>(con->rhs)) {
                    if (rhs_col->is_aggregation) {
                        std::cerr << "analyze::Error: Aggregate functions are not allowed in WHERE clause" << std::endl;
                        throw GroupByFuncError("analyze::Error: Aggregate functions are not allowed in WHERE clause");
                    }
                }
            }

            // 处理 WHERE 条件
            get_clause(x->conds, query->conds);
            check_clause(where_context_tables, query->conds);

            // 处理JOIN的ON条件
            if (join_expr) {
                if (join_expr->type == ::SEMI_JOIN) {
                    // SEMI JOIN处理
                    std::vector<Condition> semi_join_on_conditions;
                    get_clause(semi_join_expr->conds, semi_join_on_conditions);
                    // ON 子句的上下文包含左表和右表 (需要解析为真实表名)
                    std::string real_left_join_table = semi_join_left_table_name;
                    auto it_left_join = query->table_aliases.find(semi_join_left_table_name);
                    if (it_left_join != query->table_aliases.end()) {
                        real_left_join_table = it_left_join->second;
                    }
                    std::string real_right_join_table = semi_join_right_table_name;
                    auto it_right_join = query->table_aliases.find(semi_join_right_table_name);
                    if (it_right_join != query->table_aliases.end()) {
                        real_right_join_table = it_right_join->second;
                    }
                    check_clause({real_left_join_table, real_right_join_table}, semi_join_on_conditions);
                    // 注意：分析后的 ON 条件目前没有显式存储到 Query 对象中，
                    // 但已经过验证。优化器将使用 AST 的 jointree。
                } else if (!x->jointree.empty()) { // 处理其他类型的 JOIN (非 SEMI)
                    // 构建所有参与查询的真实表名列表，用于 ON 条件的检查上下文
                    std::vector<std::string> all_real_tables_in_query_for_on;
                    for (const auto& name_or_alias : query->tables) {
                        auto it = query->table_aliases.find(name_or_alias);
                        if (it != query->table_aliases.end()) {
                            all_real_tables_in_query_for_on.push_back(it->second);
                        } else {
                            all_real_tables_in_query_for_on.push_back(name_or_alias);
                        }
                    }

                    // 遍历 jointree 中的所有 JOIN 条件进行检查
                    for(const auto& current_join_expr_node : x->jointree) {
                        // 确保只处理非 SEMI_JOIN 的节点，因为 SEMI_JOIN 已单独处理
                        if (current_join_expr_node && current_join_expr_node->type != ::SEMI_JOIN) {
                            std::vector<Condition> current_on_conditions;
                            get_clause(current_join_expr_node->conds, current_on_conditions);
                            // ON 条件的上下文应该是所有参与查询的真实表名
                            check_clause(all_real_tables_in_query_for_on, current_on_conditions);
                            // 将验证后的条件存入 query->join_conds
                            query->join_conds.insert(query->join_conds.end(), current_on_conditions.begin(), current_on_conditions.end());
                        }
                    }
                }
            }
            
            // 在SelectStmt分析的最后，将query->tables规范化为真实表名
            std::vector<std::string> real_query_tables;
            for (const auto& name_or_alias : query->tables) {
                auto it = query->table_aliases.find(name_or_alias);
                if (it != query->table_aliases.end()) {
                    real_query_tables.push_back(it->second);
                } else {
                    real_query_tables.push_back(name_or_alias);
                }
            }
            query->tables = real_query_tables; // 更新query->tables为真实表名

            // 重置别名映射
            current_query_aliases_ = nullptr;
    }
    else if (auto x = std::dynamic_pointer_cast<ast::UpdateStmt>(parse)) {
        // 处理 UPDATE 语句
        query->tables = { x->tab_name };
        
        // 检查表是否存在
        for (const auto& tab_name : query->tables) {
            if (!sm_manager_->db_.is_table(tab_name)) {
                throw TableNotFoundError(tab_name);
            }
        }
        
        // 处理 SET 子句
        for (const auto& set_clause : x->set_clauses) {
            auto col_name = set_clause->col_name;
            // 在analyze阶段，保存表达式，在执行阶段再计算
            SetClause new_clause;
            new_clause.lhs = {x->tab_name, col_name};
            new_clause.expr = set_clause->expr;
            
            // 对表达式进行语义检查（验证列存在性等）
            validate_expression(set_clause->expr, x->tab_name);
            
            query->set_clauses.push_back(new_clause);
        }

        // 处理 WHERE 条件
        get_clause(x->conds, query->conds);
        check_clause(query->tables, query->conds);
    }
    else if (auto x = std::dynamic_pointer_cast<ast::DeleteStmt>(parse)) {
        // 处理 DELETE 语句的 WHERE 条件
        get_clause(x->conds, query->conds);
        check_clause({x->tab_name}, query->conds);
    }
    else if (auto x = std::dynamic_pointer_cast<ast::InsertStmt>(parse)) {
        std::string __x_tab_name = x->tab_name;
        auto tableMeta = sm_manager_->db_.get_table(__x_tab_name);
        // 处理insert 的values值
        for (size_t i = 0; i < x->vals.size(); i++) {
            SetClause __set_cls;
            TabCol selColumn = {.tab_name = __x_tab_name, .col_name = tableMeta.cols[i].name};
            __set_cls.lhs = selColumn;
            auto value = convert_sv_value(x->vals[i]);
            if (tableMeta.cols[i].type == TYPE_FLOAT && value.type == TYPE_INT) {
                Value tmp;
                tmp.set_float(value.int_val);
                value = tmp; // assign the value
            }
            query->values.push_back(value);
        }
    }
    else if (auto x = std::dynamic_pointer_cast<ast::ExplainStmt>(parse)) {
        // 处理 EXPLAIN 语句
        // 递归分析被解释的语句
        query = do_analyze(x->stmt);
        // 设置EXPLAIN标识
        query->is_explain = true;
        // 保持内部语句的parse，不要覆盖为ExplainStmt
        // 这样planner就能正确处理内部的SelectStmt等
        return query;
    }
    query->parse = std::move(parse);
    return query;
}


/**
 * @description: 检查列是否存在并推断表名，用于语义分析中的列验证
 * @param {vector<ColMeta>} all_cols 当前上下文中所有可用的列元信息
 * @param {TabCol} target 需要检查的目标列（可能缺少表名）
 * @return {TabCol} 验证后的列信息（包含完整的表名和列名）
 * @note: 如果列名未指定表名，会自动推断；如果指定了表名，会检查表和列的存在性
 */
TabCol Analyze::check_column(const std::vector<ColMeta> &all_cols, const std::vector<std::string> &context_tables, TabCol target) {
    // 解析别名 - 如果target.tab_name是别名，替换为实际表名
    if (!target.tab_name.empty() && current_query_aliases_) {
        auto it = current_query_aliases_->find(target.tab_name);
        if (it != current_query_aliases_->end()) {
            target.tab_name = it->second;  // 用实际表名替换别名
        }
    }
    
    if (target.tab_name.empty()) {
        // Table name not specified, infer table name from column name
        std::string tab_name;
        for (auto &col : all_cols) {
            if (col.name == target.col_name) {
                if (!tab_name.empty()) {
                    throw AmbiguousColumnError(target.col_name);
                }
                tab_name = col.tab_name;
            }
        }
        if (tab_name.empty()) {
            throw ColumnNotFoundError(target.col_name);
        }
        target.tab_name = tab_name;
    } else {
                
        // 检查表是否在 context 中（仅当 context 非空时）
        if (!context_tables.empty()) {
            bool in_context = false;
            for (const auto& tn : context_tables) {
                if (tn == target.tab_name) {
                    in_context = true;
                    break;
                }
            }
            if (!in_context) {
                throw ColumnNotFoundError(target.col_name);
            }
        }
        // 检查表是否存在
        if (!sm_manager_->db_.is_table(target.tab_name)) {
            
            throw TableNotFoundError(target.tab_name);
        }
        // 检查列是否存在
        const auto &cols = sm_manager_->db_.get_table(target.tab_name).cols;
        bool found = false;
        for (const auto &col : cols) {
            if (col.name == target.col_name) {
                found = true;
                break;
            }
        }
        if (!found) {
            
            throw ColumnNotFoundError(target.col_name);
        }
        
    }
    return target;
}

/**
 * @description: 获取指定表列表中所有列的元信息
 * @param {vector<string>} tab_names 表名列表
 * @param {vector<ColMeta>} all_cols 输出参数，存储所有表的列元信息
 * @return {void}
 * @note: 用于构建当前查询上下文中可用的所有列信息
 */
void Analyze::get_all_cols(const std::vector<std::string> &tab_names, std::vector<ColMeta> &all_cols) {
    for (auto &sel_tab_name : tab_names) {
        // 这里db_不能写成get_db(), 注意要传指针
        const auto &sel_tab_cols = sm_manager_->db_.get_table(sel_tab_name).cols;
        all_cols.insert(all_cols.end(), sel_tab_cols.begin(), sel_tab_cols.end());
    }
}

/**
 * @description: 将AST中的二元表达式转换为内部Condition对象
 * @param {vector<shared_ptr<ast::BinaryExpr>>} sv_conds 语法树中的条件表达式列表
 * @param {vector<Condition>} conds 输出参数，转换后的条件列表
 * @return {void}
 * @note: 处理WHERE子句和ON子句中的条件表达式，转换操作符和操作数
 */
void Analyze::get_clause(const std::vector<std::shared_ptr<ast::BinaryExpr>> &sv_conds, std::vector<Condition> &conds) {
    conds.clear();
    for (auto &expr : sv_conds) {
        Condition cond;
        cond.lhs_col = {.tab_name = expr->lhs->tab_name, .col_name = expr->lhs->col_name};
        cond.op = convert_sv_comp_op(expr->op);
        if (auto rhs_val = std::dynamic_pointer_cast<ast::Value>(expr->rhs)) {
            cond.is_rhs_val = true;
            cond.rhs_val = convert_sv_value(rhs_val);
        } else if (!expr->in_values.empty()) {
           for (const auto& x_val_ptr : expr->in_values) {
                cond.x_rhs_vals.push_back(convert_sv_value(x_val_ptr)); // 显式转换类型并添加值
            }
            cond.is_rhs_val = 0, // false
            cond.rhs_col = cond.lhs_col;
        } else if (auto rhs_col = std::dynamic_pointer_cast<ast::Col>(expr->rhs)) {
            cond.is_rhs_val = false;
            cond.rhs_col = {.tab_name = rhs_col->tab_name, .col_name = rhs_col->col_name};
        } else if (auto x_sca_subquery = std::dynamic_pointer_cast<ast::ScalarSubqueryExpr>(expr->rhs)) {
            if(x_sca_subquery->subquery->cols.empty()) {
                std::vector<ColMeta> all_cols;
                get_all_cols(x_sca_subquery->subquery->tabs, all_cols);
                for (auto &col: all_cols) {
                    auto sel_col = std::make_shared<ast::Col>(col.tab_name, col.name);
                    x_sca_subquery->subquery->cols.push_back(sel_col);
                }
            }
            cond.is_rhs_val = 0; // false
            cond.rhs_col = TabCol{
                .tab_name = x_sca_subquery->subquery->tabs[0],
                .col_name = x_sca_subquery->subquery->cols[0]->col_name, .is_vir = true
            };
            cond.x_rhs_expr = x_sca_subquery; // to record the sub query block
        }
        conds.push_back(cond);// push into the queue
    }
}

/**
 * @description: 检查条件子句的有效性，包括列存在性和类型兼容性
 * @param {vector<string>} tab_names 当前上下文中的表名列表
 * @param {vector<Condition>} conds 需要检查的条件列表，会被就地修改
 * @return {void}
 * @note: 设置全局上下文变量，验证列引用，处理类型转换，确保条件语义正确
 */
void Analyze::check_clause(const std::vector<std::string> &tab_names, std::vector<Condition> &conds) {
    std::vector<ColMeta> all_cols;
    get_all_cols(tab_names, all_cols);
    for (auto &cond : conds) {
        // 处理子查询
        if (auto x_sca_subquery = std::dynamic_pointer_cast<ast::ScalarSubqueryExpr>(cond.x_rhs_expr)) {
            cond.x_subquery_res = x_analyze_sca_subquery(x_sca_subquery);
        }
        cond.lhs_col = check_column(all_cols, tab_names, cond.lhs_col);
        if (!cond.is_rhs_val) {
            cond.rhs_col = check_column(all_cols, tab_names, cond.rhs_col);
        }
        TabMeta &lhs_tab = sm_manager_->db_.get_table(cond.lhs_col.tab_name);
        cond.lhs = lhs_tab.get_col(cond.lhs_col.col_name);
        ColType lhs_type = cond.lhs->type;
        ColType rhs_type;
        if (cond.is_rhs_val) {
            cond.rhs_val.init_raw(cond.lhs->len);
            rhs_type = cond.rhs_val.type;
        } else {
            TabMeta &rhs_tab = sm_manager_->db_.get_table(cond.rhs_col.tab_name);
            cond.rhs = rhs_tab.get_col(cond.rhs_col.col_name);
            rhs_type = cond.rhs->type;
        }
        if (lhs_type != rhs_type) {
            if(lhs_type == TYPE_FLOAT && rhs_type == TYPE_INT) {
                cond.rhs_val.set_float((float)cond.rhs_val.int_val);
            } else if (lhs_type == TYPE_INT && rhs_type == TYPE_FLOAT) {
                cond.rhs_val.set_int((int)cond.rhs_val.float_val);
            }
            else {
                throw IncompatibleTypeError(coltype2str(lhs_type), coltype2str(rhs_type));
            }
        }
    }
}

/**
 * @description: Convert Syntax Value to Value
 * @param {shared_ptr<ast::Value>} sv_val
 * @return {Value}
 */
Value Analyze::convert_sv_value(const std::shared_ptr<ast::Value> &sv_val) {
    Value val; // This is common::Value
    if (auto int_lit = std::dynamic_pointer_cast<ast::IntLit>(sv_val)) {
        val.set_int(int_lit->val);
        val.raw_literal_text = int_lit->raw_text; // Copy raw text
    } else if (auto float_lit = std::dynamic_pointer_cast<ast::FloatLit>(sv_val)) {
        val.set_float(float_lit->val);
        val.raw_literal_text = float_lit->raw_text; // Copy raw text
    } else if (auto str_lit = std::dynamic_pointer_cast<ast::StringLit>(sv_val)) {
        val.set_str(str_lit->val); // str_lit->val is content without quotes
        val.raw_literal_text = str_lit->raw_text; // Copy raw text (original with quotes)
    } else if (auto bool_lit = std::dynamic_pointer_cast<ast::BoolLit>(sv_val)) {
        val.set_int(bool_lit->val ? 1 : 0); // Example: store as int
        val.raw_literal_text = bool_lit->val ? "TRUE" : "FALSE"; // Store raw text for booleans
    }
    else {
        throw InternalError("Unexpected sv value type for common::Value conversion");
    }
    return val;
}

/**
 * @description:  Convert Syntax Value Comparison Operator to Comparison Operator
 * @param {ast::SvCompOp} op
 * @return {CompOp}
 * @note: 这里的op是ast::SvCompOp类型的，表示语法树中的比较操作符
 */

CompOp Analyze::convert_sv_comp_op(ast::SvCompOp op) {
    std::map<ast::SvCompOp, CompOp> m = {
        {ast::SV_OP_EQ, OP_EQ}, {ast::SV_OP_NE, OP_NE}, {ast::SV_OP_LT, OP_LT},
        {ast::SV_OP_GT, OP_GT}, {ast::SV_OP_LE, OP_LE}, {ast::SV_OP_GE, OP_GE},
    };
    return m.at(op);
}

AggregationOp Analyze::convert_sv_to_ag(ast::SvAggregationType info) {
    std::map<ast::SvAggregationType, AggregationOp> dix = {
        {ast::SV_AGGRE_COUNT,   AG_COUNT},
        {ast::SV_AGGRE_MAX,     AG_MAX},
        {ast::SV_AGGRE_MIN,     AG_MIN},
        {ast::SV_AGGRE_SUM,     AG_SUM},
        {ast::SV_AGGRE_AVG,     AG_AVG}
    };
    return dix[info];
}

/**
 * @description: 验证表达式的语义正确性，包括列存在性和类型兼容性
 * @param {shared_ptr<ast::Expr>} expr 需要验证的表达式
 * @param {string} tab_name 表达式所在的表名
 * @return {void}
 * @note: 递归验证表达式中的所有节点，确保列引用有效，类型兼容
 */
void Analyze::validate_expression(const std::shared_ptr<ast::Expr> &expr, const std::string &tab_name) {
    if (auto val = std::dynamic_pointer_cast<ast::Value>(expr)) {
        // 值表达式，总是有效的
        return;
    } else if (auto col = std::dynamic_pointer_cast<ast::Col>(expr)) {
        // 列引用，需要检查列是否存在
        std::vector<ColMeta> all_cols;
        get_all_cols({tab_name}, all_cols);
        TabCol target_col = {col->tab_name.empty() ? tab_name : col->tab_name, col->col_name};
        check_column(all_cols, {tab_name}, target_col);
    } else if (auto arith = std::dynamic_pointer_cast<ast::ArithmeticExpr>(expr)) {
        // 算术表达式，递归验证左右操作数
        validate_expression(arith->lhs, tab_name);
        validate_expression(arith->rhs, tab_name);
        
        // 可以在这里添加类型检查逻辑
        // 例如，确保字符串不能参与算术运算等
    } else {
        throw InternalError("Unsupported expression type in SET clause");
    }
}

std::shared_ptr<Query> Analyze::x_analyze_sca_subquery(std::shared_ptr<ast::ScalarSubqueryExpr> expr) {
    auto subquery_result = do_analyze(expr->subquery);
    if (subquery_result->cols.size() != 1) throw InternalError("Scalar subquery must return exactly one column.");
    return subquery_result;
}

