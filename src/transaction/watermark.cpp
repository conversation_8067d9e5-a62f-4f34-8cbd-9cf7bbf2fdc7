/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "transaction/watermark.h"


auto Watermark::AddTxn(timestamp_t read_ts) -> void {
    // 增加指定读时间戳的事务计数
    current_reads_[read_ts]++;

    // 更新水位线：水位线是所有活跃事务中最小的读时间戳
    // 如果这是第一个具有此读时间戳的事务，可能需要更新水位线
    if (current_reads_.size() == 1 || read_ts < watermark_) {
        watermark_ = read_ts;
    }
}

auto Watermark::RemoveTxn(timestamp_t read_ts) -> void {
    auto it = current_reads_.find(read_ts);
    if (it != current_reads_.end()) {
        it->second--;

        // 如果这个读时间戳的事务计数降为0，移除它
        if (it->second <= 0) {
            current_reads_.erase(it);
        }

        // 重新计算水位线：找到所有活跃事务中最小的读时间戳
        if (current_reads_.empty()) {
            // 如果没有活跃事务，水位线设为当前提交时间戳
            watermark_ = commit_ts_;
        } else {
            // 水位线是所有活跃事务中最小的读时间戳
            watermark_ = current_reads_.begin()->first;
        }
    }
}

void Watermark::UpdateCommitTs(timestamp_t commit_ts) {
    commit_ts_ = commit_ts;

    // 如果没有活跃事务，更新水位线为新的提交时间戳
    if (current_reads_.empty()) {
        watermark_ = commit_ts_;
    }
}

timestamp_t Watermark::GetWatermark() {
    return watermark_;
}