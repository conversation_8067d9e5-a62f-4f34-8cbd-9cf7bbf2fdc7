/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "mvcc_helper.h"
#include "transaction_manager.h"

// 临时存储，模拟TupleMeta与记录的关联
std::unordered_map<std::string, TupleMeta> MVCCHelper::temp_tuple_meta_map_;

bool MVCCHelper::IsVisible(const TupleMeta& tuple_meta, Transaction* current_txn,
                          TransactionManager* txn_mgr) {
    if (current_txn == nullptr) {
        return false;
    }

    // 1. 如果这个元组是当前事务创建的，那么它对当前事务总是可见的
    if (tuple_meta.creator_txn_id_ == current_txn->get_transaction_id()) {
        return true;
    }

    // 2. 检查创建者事务ID是否有效
    if (tuple_meta.creator_txn_id_ == INVALID_TXN_ID) {
        return true;  // 无效事务ID，认为是系统数据或已提交的旧数据
    }

    // 3. 获取创建者事务
    Transaction* creator_txn = txn_mgr->get_transaction(tuple_meta.creator_txn_id_);

    if (creator_txn == nullptr) {
        // 创建者事务不在活跃事务表中，说明已经提交或中止并被清理
        // 根据MVCC快照隔离，需要检查记录的时间戳
        // 如果记录的时间戳小于等于当前事务的读时间戳，则可见
        return tuple_meta.ts_ <= current_txn->get_read_ts();
    }
    
    // 4. 检查创建者事务的状态
    TransactionState creator_state = creator_txn->get_state();
    
    switch (creator_state) {
        case TransactionState::COMMITTED:
            // 创建者事务已提交，检查提交时间戳与当前事务的读时间戳
            {
                timestamp_t commit_ts = creator_txn->get_commit_ts();
                timestamp_t read_ts = current_txn->get_read_ts();

                // 只有在当前事务开始之前或同时提交的版本才可见
                // 使用小于等于来确保快照一致性
                return commit_ts <= read_ts;
            }
            
        case TransactionState::ABORTED:
            // 已中止的事务创建的版本不可见
            return false;
            
        case TransactionState::DEFAULT:
        case TransactionState::GROWING:
        case TransactionState::SHRINKING:
            // 正在运行的事务创建的版本对其他事务不可见
            return false;
            
        default:
            // 未知状态，保守起见认为不可见
            return false;
    }
}

bool MVCCHelper::CheckWriteConflict(const TupleMeta& tuple_meta, Transaction* current_txn,
                                   TransactionManager* txn_mgr) {
    if (current_txn == nullptr || txn_mgr == nullptr) {
        return true;  // 参数无效，认为有冲突
    }

    // 1. 如果当前事务就是创建者，没有冲突
    if (tuple_meta.creator_txn_id_ == current_txn->get_transaction_id()) {
        return false;
    }

    // 3. 如果创建者事务ID无效，认为是系统数据，允许修改
    if (tuple_meta.creator_txn_id_ == INVALID_TXN_ID) {
        return false;  // 无冲突
    }

    // 4. 获取创建者事务
    Transaction* creator_txn = txn_mgr->get_transaction(tuple_meta.creator_txn_id_);

    if (creator_txn != nullptr) {
        TransactionState creator_state = creator_txn->get_state();

        // 情况5：创建者事务仍然活跃（未提交）
        if (creator_state == TransactionState::DEFAULT ||
            creator_state == TransactionState::GROWING ||
            creator_state == TransactionState::SHRINKING) {
            // 写写冲突情况(1)：最新版本属于未提交事务
            return true;  // 有冲突，当前事务需要abort
        }

        // 情况6：创建者事务已提交
        if (creator_state == TransactionState::COMMITTED) {
            timestamp_t creator_commit_ts = creator_txn->get_commit_ts();
            timestamp_t current_read_ts = current_txn->get_read_ts();

            // 写写冲突情况(2)：最新版本属于已提交事务，且提交时间戳 > 当前事务读时间戳
            if (creator_commit_ts > current_read_ts) {
                return true;  // 有冲突，当前事务需要abort
            }

            return false;  // 无冲突，允许修改
        }

        // 情况7：创建者事务已中止
        if (creator_state == TransactionState::ABORTED) {
            // 已中止事务创建的记录应该不可见，但如果仍然存在，可能是清理未完成
            // 为安全起见，认为有冲突
            return true;
        }
    }

    // 情况8：创建者事务不在活跃事务表中
    // 这种情况下，事务可能已经提交并被清理，或者从未存在
    // 我们需要根据记录的时间戳进行判断
    if (tuple_meta.ts_ > current_txn->get_read_ts()) {
        // 记录的时间戳大于当前事务的读时间戳，可能存在冲突
        return true;
    }

    // 默认情况：假设记录是在当前事务开始前创建的，允许修改
    return false;  // 无冲突，允许修改
}

TupleMeta MVCCHelper::ExtractTupleMeta(const RmRecord& record, const Rid& rid) {
    // 真实实现：从记录头部提取TupleMeta
    if (record.size < static_cast<int>(sizeof(TupleMeta))) {
        // 如果记录太小，返回默认的TupleMeta（兼容旧格式）
        TupleMeta default_meta;
        default_meta.ts_ = 0;
        default_meta.is_deleted_ = false;
        default_meta.creator_txn_id_ = INVALID_TXN_ID;
        default_meta.prev_version_ptr_ = INVALID_UNDO_PTR;
        return default_meta;
    }
    
    TupleMeta meta;
    memcpy(&meta, record.data, sizeof(TupleMeta));
    return meta;
}

void MVCCHelper::SetTupleMeta(RmRecord& record, const TupleMeta& tuple_meta, const Rid& rid) {
    // 真实实现：将TupleMeta写入记录头部
    if (record.size >= static_cast<int>(sizeof(TupleMeta))) {
        memcpy(record.data, &tuple_meta, sizeof(TupleMeta));
    }
}

std::unique_ptr<RmRecord> MVCCHelper::GetUserData(const RmRecord& record) {
    if (record.size <= static_cast<int>(sizeof(TupleMeta))) {
        // 记录太小，返回空记录
        return std::make_unique<RmRecord>(0);
    }
    
    int user_data_size = record.size - sizeof(TupleMeta);
    char* user_data_start = record.data + sizeof(TupleMeta);
    
    return std::make_unique<RmRecord>(user_data_size, user_data_start);
}

std::string MVCCHelper::GetRecordKey(const Rid& rid) {
    return std::to_string(rid.page_no) + "_" + std::to_string(rid.slot_no);
}

bool MVCCHelper::ReconstructTuple(const RmRecord& record, const TupleMeta& tuple_meta,
                                 Transaction* current_txn, TransactionManager* txn_mgr,
                                 UndoLogManager* undo_mgr,
                                 RmRecord& visible_record, TupleMeta& visible_meta) {
    // 首先检查当前版本是否可见
    if (IsVisible(tuple_meta, current_txn, txn_mgr)) {
        // 如果当前版本可见且未被删除，直接返回
        if (!tuple_meta.is_deleted_) {
            visible_record = record;
            visible_meta = tuple_meta;
            return true;
        }
        // 如果当前版本已被删除且可见，说明记录对当前事务来说是已删除的
        // 但我们仍需要检查版本链中是否有更早的可见版本
    }

    // 沿着版本链向后查找可见版本
    undo_ptr_t current_undo_ptr = tuple_meta.prev_version_ptr_;
    TupleMeta current_meta = tuple_meta;
    
    while (current_undo_ptr != INVALID_UNDO_PTR) {
        // 获取撤销日志记录
        auto undo_log = undo_mgr->GetUndoLog(current_undo_ptr);
        if (!undo_log) {
            break;  // 撤销日志不存在，停止查找
        }
        
        // 重构前一个版本的TupleMeta
        TupleMeta prev_meta;

        // 根据撤销日志类型推断前一个版本的状态
        if (undo_log->header_.log_type_ == UndoLogType::DELETE) {
            // 如果是删除操作的撤销日志，说明前一个版本是存在的（未删除）
            prev_meta.is_deleted_ = false;
        } else if (undo_log->header_.log_type_ == UndoLogType::UPDATE) {
            // 如果是更新操作的撤销日志，前一个版本的删除状态需要继续向前查找
            // 这里假设更新操作不会改变删除状态
            prev_meta.is_deleted_ = false;
        }

        // 设置前一个版本的其他元数据
        prev_meta.creator_txn_id_ = undo_log->header_.creator_txn_id_;
        prev_meta.prev_version_ptr_ = undo_log->header_.prev_undo_ptr_;

        // 时间戳需要从创建者事务获取，如果事务不存在，使用记录中的时间戳
        auto creator_txn = txn_mgr->get_transaction(prev_meta.creator_txn_id_);
        if (creator_txn != nullptr) {
            // 如果事务已提交，使用提交时间戳
            if (creator_txn->get_state() == TransactionState::COMMITTED) {
                prev_meta.ts_ = creator_txn->get_commit_ts();
            } else {
                // 如果事务还未提交，使用读时间戳
                prev_meta.ts_ = creator_txn->get_read_ts();
            }
        } else {
            // 事务已经不存在，说明已经提交
            // 使用一个很早的时间戳，确保对所有后续事务都可见
            prev_meta.ts_ = 0;
        }
        
        // 检查这个版本是否可见
        if (IsVisible(prev_meta, current_txn, txn_mgr)) {
            // 如果找到可见版本且未被删除
            if (!prev_meta.is_deleted_) {
                // 重构记录数据 - 创建包含TupleMeta的完整记录
                // 注意：undo_log中存储的是用户数据，我们需要重构完整记录
                
                // 创建完整记录大小 = TupleMeta大小 + 用户数据大小
                int full_record_size = sizeof(TupleMeta) + undo_log->header_.record_size_;
                visible_record = RmRecord(full_record_size);
                
                // 设置TupleMeta部分
                memcpy(visible_record.data, &prev_meta, sizeof(TupleMeta));
                
                // 设置用户数据部分
                memcpy(visible_record.data + sizeof(TupleMeta), 
                       undo_log->data_, undo_log->header_.record_size_);
                
                visible_meta = prev_meta;
                return true;
            }
            // 如果这个版本已被删除但可见，继续向前查找
        }
        
        // 移动到下一个版本
        current_undo_ptr = undo_log->header_.prev_undo_ptr_;
        current_meta = prev_meta;
    }
    
    // 没有找到可见的未删除版本
    return false;
}

bool MVCCHelper::ReconstructFromUndoLog(const UndoLogRecord& undo_log, RmRecord& reconstructed_record) {
    if (undo_log.data_ == nullptr || undo_log.header_.record_size_ <= 0) {
        return false;
    }
    
    // 从撤销日志重构记录
    reconstructed_record = RmRecord(undo_log.header_.record_size_, undo_log.data_);
    return true;
} 