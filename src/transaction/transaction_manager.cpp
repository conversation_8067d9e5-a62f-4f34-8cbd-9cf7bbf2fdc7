/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "transaction_manager.h"
#include "record/rm_file_handle.h"
#include "system/sm_manager.h"

std::unordered_map<txn_id_t, Transaction *> TransactionManager::txn_map = {};

/**
 * @description: 事务的开始方法
 * @return {Transaction*} 开始事务的指针
 * @param {Transaction*} txn 事务指针，空指针代表需要创建新事务，否则开始已有事务
 * @param {LogManager*} log_manager 日志管理器指针
 */
Transaction * TransactionManager::begin(Transaction* txn, LogManager* log_manager) {
    if (txn == nullptr) {
        txn = new Transaction(next_txn_id_++);
    }
    
    // MVCC: 为事务设置读时间戳
    if (concurrency_mode_ == ConcurrencyMode::MVCC) {
        // 读时间戳设置为当前的逻辑时钟值
        // 这确保事务能够看到所有已提交的数据
        timestamp_t read_ts = allocate_timestamp();

        txn->set_read_ts(read_ts);

        // 将事务添加到活跃事务的水位线中
        running_txns_.AddTxn(read_ts);
    }
    
    txn_map[txn->get_transaction_id()] = txn;

    if (log_manager != nullptr) {
        BeginLogRecord beginLog(txn->get_transaction_id());
        log_manager->add_log_to_buffer(&beginLog);
    }
    return txn;
}

/**
 * @description: 事务的提交方法
 * @param {Transaction*} txn 需要提交的事务
 * @param {LogManager*} log_manager 日志管理器指针
 */
void TransactionManager::commit(Transaction* txn, LogManager* log_manager) {
    // 获取互斥锁保证原子性操作（RAII机制自动释放）
    std::scoped_lock lock(latch_);

    // 空事务直接返回
    if (txn == nullptr) {
        return;
    }

    // MVCC: 分配提交时间戳并更新全局状态
    if (concurrency_mode_ == ConcurrencyMode::MVCC) {
        // 分配提交时间戳，确保大于所有已存在的read_ts和commit_ts
        timestamp_t commit_ts = allocate_timestamp();
        
        txn->set_commit_ts(commit_ts);
        
        // 更新全局最新已提交时间戳
        update_latest_committed_ts(commit_ts);

        // 更新水位线的提交时间戳
        running_txns_.UpdateCommitTs(commit_ts);

        // MVCC关键：更新该事务创建的所有记录的时间戳为提交时间戳
        // 注意：这里我们需要遍历事务的写集合来更新记录时间戳
        // 但由于当前架构限制，我们依赖于可见性检查时动态获取提交时间戳

        // 从活跃事务水位线中移除该事务
        running_txns_.RemoveTxn(txn->get_read_ts());
    }

    // 获取事务的写操作集合和锁集合
    auto write_opt_set = txn->get_write_set();
    auto lock_set = txn->get_lock_set();

    // 清空事务的写操作集合（循环弹出避免内存泄漏）
    while (!write_opt_set->empty()) {
        write_opt_set->pop_back();  // 逐个移除写操作记录
    }

    // 释放事务持有的所有锁资源
    for (auto lock_index : *lock_set) {
        lock_manager_->unlock(txn, lock_index);  // 通过锁管理器释放单个锁
    }
    lock_set->clear();  // 清空锁集合

    // 更新事务状态为已提交
    txn->set_state(TransactionState::COMMITTED);  // 设置状态机最终状态
    
    // 注意：不在这里删除事务，让调用者负责清理
    // txn_map.erase(txn->get_transaction_id());
}

/**
 * @description: 事务的终止（回滚）方法
 * @param {Transaction *} txn 需要回滚的事务
 * @param {LogManager} *log_manager 日志管理器指针
 */
void TransactionManager::abort(Transaction* txn, LogManager* log_manager) {
    if (txn == nullptr) {
        return;
    }

    // For MVCC, aborting is simpler. We just mark the transaction as aborted.
    // The versions created by this transaction will be invisible to others.
    // The undo logs will be used by a garbage collector to clean up later.
    if (concurrency_mode_ == ConcurrencyMode::MVCC) {
        // 从活跃事务水位线中移除该事务
        running_txns_.RemoveTxn(txn->get_read_ts());
        
        // 更新事务状态为已中止
        txn->set_state(TransactionState::ABORTED);

        // MVCC模式下仍然可能使用锁（例如，对索引的latch），所以需要释放
        auto lockSet = txn->get_lock_set();
        for(auto it : *lockSet){
            lock_manager_->unlock(txn, it);
        }
        lockSet->clear();
        
        // 清空旧的写操作集合（尽管在纯MVCC中可能不使用）
        auto writeSet = txn->get_write_set();
        while (!writeSet->empty()) {
            writeSet->pop_back();
        }
        writeSet->clear();

        return; // MVCC的abort操作到此结束
    }
        
    // --- 保持原有的非MVCC回滚逻辑 ---
    auto writeSet = txn->get_write_set();
    while (!writeSet->empty()) {
        auto& item = writeSet->back();
        // 注意：这里的Context创建可能不完整，但为了保持原有逻辑不变，暂时保留
        auto context = std::make_shared<Context>(lock_manager_, log_manager, txn);
        auto tab_file_handle = sm_manager_->fhs_[item->GetTableName()].get();
        auto tab_name = item->GetTableName();
        auto& tab_meta = sm_manager_->db_.get_table(tab_name);
        auto& rid = item->GetRid();
        auto& record = item->GetRecord();
        switch (item->GetWriteType()) {
        case WType::INSERT_TUPLE:{
            // 先删除索引再删除记录
            for (auto& index : tab_meta.indexes) {
                auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name, index.second.cols)).get();
                auto key = index.second.gen_index_key(record.data);
                ih->delete_entry(key.get(), txn);
            }
            tab_file_handle->delete_record(rid, context.get());
            break;
        }
        case WType::UPDATE_TUPLE:{
            auto& old_record = item->GetOldRecord();
            for (auto& index : tab_meta.indexes) {
                auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name, index.second.cols)).get();
                auto key = index.second.gen_index_key(record.data);
                ih->delete_entry(key.get(), txn);
            }
             // 恢复旧值
            tab_file_handle->update_record(rid, old_record.data, context.get());
            for (auto& index : tab_meta.indexes) {
                auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name, index.second.cols)).get();
                auto key = index.second.gen_index_key(old_record.data);
                ih->insert_entry(key.get(), rid, txn);
            }
            break;
        }
        case WType::DELETE_TUPLE:{
            // 先插入记录再插入索引
            tab_file_handle->insert_record(rid, record.data);
            for (auto& index : tab_meta.indexes) {
                auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name, index.second.cols)).get();
                auto key = index.second.gen_index_key(record.data);
                ih->insert_entry(key.get(), rid, txn);
            }
            break;
        }
        default:
            break;
        }
        writeSet->pop_back();
    }
    writeSet->clear();

    auto lockSet = txn->get_lock_set();
    for(auto it : *lockSet){
        lock_manager_->unlock(txn, it);
    }
    lockSet->clear();
    txn->set_state(TransactionState::ABORTED);
    
    // 注意：不在这里删除事务，让调用者负责清理
    // txn_map.erase(txn->get_transaction_id());
}