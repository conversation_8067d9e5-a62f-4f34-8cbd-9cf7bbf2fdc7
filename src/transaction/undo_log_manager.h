/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include <memory>
#include <mutex>
#include <unordered_map>
#include <vector>
#include "record/rm_defs.h"
#include "common/config.h"

/**
 * @brief 撤销日志管理器
 * 
 * 负责管理所有事务的撤销日志，包括撤销日志的创建、存储、检索和清理
 */
class UndoLogManager {
public:
    UndoLogManager() = default;
    ~UndoLogManager() = default;

    /**
     * @brief 创建一个新的撤销日志记录
     * 
     * @param log_type 撤销日志类型
     * @param creator_txn_id 创建此撤销日志的事务ID
     * @param prev_undo_ptr 指向更早版本的撤销日志指针
     * @param record_size 记录数据的大小
     * @param data 旧版本的记录数据
     * @return undo_ptr_t 返回新创建的撤销日志指针
     */
    undo_ptr_t CreateUndoLog(UndoLogType log_type, txn_id_t creator_txn_id,
                            undo_ptr_t prev_undo_ptr, int record_size, const char* data);

    /**
     * @brief 根据撤销日志指针获取撤销日志记录
     * 
     * @param undo_ptr 撤销日志指针
     * @return std::shared_ptr<UndoLogRecord> 撤销日志记录的智能指针，如果不存在则返回nullptr
     */
    std::shared_ptr<UndoLogRecord> GetUndoLog(undo_ptr_t undo_ptr);

    /**
     * @brief 清理指定事务的所有撤销日志
     * 
     * 当事务提交后，可以清理该事务创建的撤销日志以释放内存
     * 
     * @param txn_id 事务ID
     */
    void CleanupTransactionUndoLogs(txn_id_t txn_id);

    /**
     * @brief 获取撤销日志的总数（用于调试和监控）
     * 
     * @return size_t 撤销日志的总数
     */
    size_t GetUndoLogCount() const;

    /**
     * @brief 清理所有撤销日志（主要用于测试和重置）
     */
    void ClearAllUndoLogs();

private:
    mutable std::mutex undo_log_mutex_;  // 保护撤销日志存储的互斥锁
    
    // 撤销日志存储，使用undo_ptr_t作为键
    std::unordered_map<undo_ptr_t, std::shared_ptr<UndoLogRecord>> undo_log_storage_;
    
    // 事务ID到其创建的撤销日志指针列表的映射，用于快速清理
    std::unordered_map<txn_id_t, std::vector<undo_ptr_t>> txn_undo_logs_;
    
    // 撤销日志指针生成器，确保每个撤销日志都有唯一的指针
    std::atomic<undo_ptr_t> next_undo_ptr_{1};  // 从1开始，0保留为无效值
}; 