/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "record/rm_defs.h"
#include "transaction.h"
#include "undo_log_manager.h"
#include <memory>

class TransactionManager;
class UndoLogManager;

/**
 * @brief MVCC助手类，提供与多版本并发控制相关的工具函数
 */
class MVCCHelper {
public:
    /**
     * @brief 检查给定元组版本对指定事务是否可见
     * @param tuple_meta 元组的版本信息
     * @param current_txn 当前事务
     * @param txn_mgr 事务管理器
     * @return true 如果版本可见，false 否则
     */
    static bool IsVisible(const TupleMeta& tuple_meta, Transaction* current_txn, 
                         TransactionManager* txn_mgr);

    /**
     * @brief 沿着版本链重构出对指定事务可见的元组版本
     * @param record 当前记录数据
     * @param tuple_meta 当前记录的版本信息
     * @param current_txn 当前事务
     * @param txn_mgr 事务管理器
     * @param undo_mgr 撤销日志管理器
     * @param visible_record 输出参数：可见的记录数据
     * @param visible_meta 输出参数：可见记录的版本信息
     * @return true 如果找到可见版本，false 否则（记录已被删除或不可见）
     */
    static bool ReconstructTuple(const RmRecord& record, const TupleMeta& tuple_meta,
                                Transaction* current_txn, TransactionManager* txn_mgr,
                                UndoLogManager* undo_mgr,
                                RmRecord& visible_record, TupleMeta& visible_meta);

    /**
     * @brief 检查写入冲突 - 阶段四新增
     * 在事务T_A尝试修改元组时，检查是否存在写入冲突
     * @param tuple_meta 要修改的记录的当前版本信息
     * @param current_txn 当前尝试修改的事务T_A
     * @param txn_mgr 事务管理器
     * @return true 如果存在冲突（需要中止当前事务），false 如果允许修改
     */
    static bool CheckWriteConflict(const TupleMeta& tuple_meta, Transaction* current_txn,
                                  TransactionManager* txn_mgr);

    /**
     * @brief 从记录中提取TupleMeta信息 - 临时实现
     * 注意：这是一个临时实现，等待TupleMeta完全集成到记录存储中
     * @param record 记录数据
     * @param rid 记录的位置标识
     * @return TupleMeta 提取的版本信息（当前返回默认值）
     */
    static TupleMeta ExtractTupleMeta(const RmRecord& record, const Rid& rid);

    /**
     * @brief 设置记录的TupleMeta信息 - 真实实现
     * @param record 记录数据
     * @param tuple_meta 要设置的版本信息
     * @param rid 记录的位置标识
     */
    static void SetTupleMeta(RmRecord& record, const TupleMeta& tuple_meta, const Rid& rid);
    
    /**
     * @brief 从完整记录中提取用户数据部分
     * @param record 完整的记录（包含TupleMeta + 用户数据）
     * @return RmRecord 只包含用户数据的记录
     */
    static std::unique_ptr<RmRecord> GetUserData(const RmRecord& record);

    /**
     * @brief 从撤销日志重构记录（公开方法，用于测试）
     * 
     * @param undo_log 撤销日志记录
     * @param reconstructed_record 输出参数，重构的记录
     * @return true 如果重构成功
     * @return false 如果重构失败
     */
    static bool ReconstructFromUndoLog(const UndoLogRecord& undo_log, RmRecord& reconstructed_record);

private:
    // 临时存储，模拟TupleMeta与记录的关联
    // 在真实实现中，TupleMeta应该直接存储在记录的头部
    static std::unordered_map<std::string, TupleMeta> temp_tuple_meta_map_;
    
    /**
     * @brief 生成记录的唯一键，用于临时存储
     * @param rid 记录位置
     * @return 唯一键字符串
     */
    static std::string GetRecordKey(const Rid& rid);
}; 