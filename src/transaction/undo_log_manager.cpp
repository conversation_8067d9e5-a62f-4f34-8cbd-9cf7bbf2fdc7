/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "undo_log_manager.h"
#include <algorithm>

undo_ptr_t UndoLogManager::CreateUndoLog(UndoLogType log_type, txn_id_t creator_txn_id,
                                         undo_ptr_t prev_undo_ptr, int record_size, const char* data) {
    std::lock_guard<std::mutex> lock(undo_log_mutex_);
    
    // 生成新的撤销日志指针
    undo_ptr_t new_undo_ptr = next_undo_ptr_.fetch_add(1);
    
    // 创建撤销日志记录
    auto undo_log = std::make_shared<UndoLogRecord>(log_type, creator_txn_id, prev_undo_ptr, record_size, data);
    
    // 存储撤销日志
    undo_log_storage_[new_undo_ptr] = undo_log;
    
    // 记录事务与撤销日志的映射关系
    txn_undo_logs_[creator_txn_id].push_back(new_undo_ptr);
    
    return new_undo_ptr;
}

std::shared_ptr<UndoLogRecord> UndoLogManager::GetUndoLog(undo_ptr_t undo_ptr) {
    if (undo_ptr == INVALID_UNDO_PTR) {
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(undo_log_mutex_);
    
    auto it = undo_log_storage_.find(undo_ptr);
    if (it != undo_log_storage_.end()) {
        return it->second;
    }
    
    return nullptr;
}

void UndoLogManager::CleanupTransactionUndoLogs(txn_id_t txn_id) {
    std::lock_guard<std::mutex> lock(undo_log_mutex_);
    
    auto txn_it = txn_undo_logs_.find(txn_id);
    if (txn_it != txn_undo_logs_.end()) {
        // 删除该事务创建的所有撤销日志
        for (undo_ptr_t undo_ptr : txn_it->second) {
            undo_log_storage_.erase(undo_ptr);
        }
        
        // 删除事务映射
        txn_undo_logs_.erase(txn_it);
    }
}

size_t UndoLogManager::GetUndoLogCount() const {
    std::lock_guard<std::mutex> lock(undo_log_mutex_);
    return undo_log_storage_.size();
}

void UndoLogManager::ClearAllUndoLogs() {
    std::lock_guard<std::mutex> lock(undo_log_mutex_);
    
    undo_log_storage_.clear();
    txn_undo_logs_.clear();
    next_undo_ptr_.store(1);  // 重置指针生成器
} 