#include "gtest/gtest.h"
#include "parser/ast.h"
// parser.h should include yacc.tab.h which declares yyparse()
// and ast::parse_tree
#include "parser/parser.h"
#include <string>
#include <vector>
#include <memory>

// Forward declarations for Flex functions if not exposed by a header
// Typically, Flex generates a lex.yy.c/cpp file.
// If a header like lex.yy.h is generated and included by parser.h or ast.h, these might not be needed.
// Assuming yacc.tab.h (included via parser.h or indirectly) declares yyparse.
extern int yyparse(); // For pure C++ parser, it might be in a namespace e.g., yy::parser::parse()

// Flex buffer management functions
// YY_BUFFER_STATE is usually defined by Flex itself, often in the generated lex.yy.c/cpp or a generated header.
// Avoid re-typedefing it here unless absolutely necessary and confirmed that Flex doesn't provide it.
extern YY_BUFFER_STATE yy_scan_string(const char *yystr);
extern void yy_delete_buffer(YY_BUFFER_STATE b);
// extern void yylex_destroy(); // If using reentrant lexer and need to clean up

// Helper function to parse SQL and get the AST root
std::shared_ptr<ast::TreeNode> parse_sql_string(const std::string& sql) {
    // Ensure Flex is set up to read from the string.
    // The semicolon is added because the grammar in yacc.y expects 'stmt ;'
    YY_BUFFER_STATE buffer = yy_scan_string((sql + ";").c_str());
    
    int result = yyparse(); // This will use the buffer set by yy_scan_string
    
    yy_delete_buffer(buffer); // Clean up the Flex buffer
    // yylex_destroy(); // If lexer has global state to clean up and is reentrant

    if (result != 0) { // 0 for success, 1 for syntax error, 2 for memory exhaustion
        // You might want to throw an exception or log an error here
        return nullptr;
    }
    return ast::parse_tree; // ast::parse_tree is the global AST root
}

TEST(SemiJoinParserTest, BasicSemiJoin) {
    std::string sql = "SELECT t1.a FROM t1 SEMI JOIN t2 ON t1.id = t2.id";
    auto tree = parse_sql_string(sql);

    ASSERT_NE(tree, nullptr) << "Parser failed for: " << sql;
    auto select_stmt = std::dynamic_pointer_cast<ast::SelectStmt>(tree);
    ASSERT_NE(select_stmt, nullptr) << "Parsed tree is not a SelectStmt";

    // Check selected columns (should be t1.a)
    ASSERT_EQ(select_stmt->cols.size(), 1);
    ASSERT_NE(select_stmt->cols[0], nullptr);
    EXPECT_EQ(select_stmt->cols[0]->tab_name, "t1");
    EXPECT_EQ(select_stmt->cols[0]->col_name, "a");

    // Check FROM table (should be t1, as per our yacc rule for SEMI JOIN)
    ASSERT_EQ(select_stmt->tabs.size(), 1);
    EXPECT_EQ(select_stmt->tabs[0], "t1");

    // Check JoinTree
    ASSERT_EQ(select_stmt->jointree.size(), 1) << "No JoinExpr found in jointree";
    ASSERT_NE(select_stmt->jointree[0], nullptr);
    auto join_expr = select_stmt->jointree[0];

    EXPECT_EQ(join_expr->left, "t1");
    EXPECT_EQ(join_expr->right, "t2");
    EXPECT_EQ(join_expr->type, ::SEMI_JOIN); // JoinType is global

    // Check Join Conditions (t1.id = t2.id)
    ASSERT_EQ(join_expr->conds.size(), 1) << "Incorrect number of join conditions";
    ASSERT_NE(join_expr->conds[0], nullptr);
    auto join_cond = join_expr->conds[0];
    ASSERT_NE(join_cond->lhs, nullptr);
    EXPECT_EQ(join_cond->lhs->tab_name, "t1");
    EXPECT_EQ(join_cond->lhs->col_name, "id");
    EXPECT_EQ(join_cond->op, ast::SvCompOp::SV_OP_EQ);
    ASSERT_NE(join_cond->rhs, nullptr);
    auto rhs_col = std::dynamic_pointer_cast<ast::Col>(join_cond->rhs);
    ASSERT_NE(rhs_col, nullptr) << "RHS of join condition is not a Column";
    EXPECT_EQ(rhs_col->tab_name, "t2");
    EXPECT_EQ(rhs_col->col_name, "id");

    // Check no outer WHERE clause
    EXPECT_TRUE(select_stmt->conds.empty());
    // Check no ORDER BY clause
    EXPECT_FALSE(select_stmt->has_sort);
}

TEST(SemiJoinParserTest, SemiJoinWithWhereClause) {
    std::string sql = "SELECT t1.b FROM t1 SEMI JOIN t2 ON t1.fk = t2.pk WHERE t1.x > 10";
    auto tree = parse_sql_string(sql);

    ASSERT_NE(tree, nullptr) << "Parser failed for: " << sql;
    auto select_stmt = std::dynamic_pointer_cast<ast::SelectStmt>(tree);
    ASSERT_NE(select_stmt, nullptr) << "Parsed tree is not a SelectStmt";

    // Check selected columns
    ASSERT_EQ(select_stmt->cols.size(), 1);
    ASSERT_NE(select_stmt->cols[0], nullptr);
    EXPECT_EQ(select_stmt->cols[0]->tab_name, "t1");
    EXPECT_EQ(select_stmt->cols[0]->col_name, "b");

    // Check FROM table
    ASSERT_EQ(select_stmt->tabs.size(), 1);
    EXPECT_EQ(select_stmt->tabs[0], "t1");

    // Check JoinTree
    ASSERT_EQ(select_stmt->jointree.size(), 1);
    ASSERT_NE(select_stmt->jointree[0], nullptr);
    auto join_expr = select_stmt->jointree[0];
    EXPECT_EQ(join_expr->left, "t1");
    EXPECT_EQ(join_expr->right, "t2");
    EXPECT_EQ(join_expr->type, ::SEMI_JOIN); // JoinType is global
    ASSERT_EQ(join_expr->conds.size(), 1); // ON t1.fk = t2.pk
    ASSERT_NE(join_expr->conds[0], nullptr);
    EXPECT_EQ(join_expr->conds[0]->lhs->col_name, "fk");
    auto join_rhs_col = std::dynamic_pointer_cast<ast::Col>(join_expr->conds[0]->rhs);
    ASSERT_NE(join_rhs_col, nullptr);
    EXPECT_EQ(join_rhs_col->col_name, "pk");


    // Check outer WHERE clause (t1.x > 10)
    ASSERT_EQ(select_stmt->conds.size(), 1);
    ASSERT_NE(select_stmt->conds[0], nullptr);
    auto outer_cond = select_stmt->conds[0];
    ASSERT_NE(outer_cond->lhs, nullptr);
    EXPECT_EQ(outer_cond->lhs->tab_name, "t1"); // Assuming unqualified column in WHERE refers to t1
    EXPECT_EQ(outer_cond->lhs->col_name, "x");
    EXPECT_EQ(outer_cond->op, ast::SvCompOp::SV_OP_GT);
    ASSERT_NE(outer_cond->rhs, nullptr);
    auto rhs_val = std::dynamic_pointer_cast<ast::IntLit>(outer_cond->rhs);
    ASSERT_NE(rhs_val, nullptr) << "RHS of outer where condition is not an IntLit";
    EXPECT_EQ(rhs_val->val, 10);
}