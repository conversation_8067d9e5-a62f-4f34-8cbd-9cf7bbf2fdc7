#include "gtest/gtest.h"
#include "parser/ast.h"
#include "parser/parser.h" // For ast::parse_tree and Flex/Bison functions
#include "analyze/analyze.h"
#include "system/sm_manager.h"
#include "storage/disk_manager.h"
#include "storage/buffer_pool_manager.h"
#include "record/rm_manager.h"
#include "index/ix_manager.h"
#include "errors.h"
#include "defs.h" // For ColType
#include "common/common.h" // For TabCol, Condition etc. if used directly by tests

#include <string>
#include <vector>
#include <memory>
#include <filesystem> // For cleaning up db files

// Forward declarations for Flex/Bison functions (similar to parser_test)
extern int yyparse();
typedef struct yy_buffer_state *YY_BUFFER_STATE;
extern YY_BUFFER_STATE yy_scan_string(const char *yystr);
extern void yy_delete_buffer(YY_BUFFER_STATE b);

// Helper function to parse SQL and get the AST root
std::shared_ptr<ast::TreeNode> parse_sql_for_analyzer_test(const std::string& sql) {
    YY_BUFFER_STATE buffer = yy_scan_string((sql + ";").c_str());
    int result = yyparse();
    yy_delete_buffer(buffer);
    if (result != 0) {
        return nullptr;
    }
    return ast::parse_tree;
}

const std::string TEST_DB_NAME = "semi_join_analyzer_test_db";
const int TEST_BUFFER_POOL_SIZE = 10; // A small buffer pool for testing

class SemiJoinAnalyzerTest : public ::testing::Test {
protected:
    DiskManager* disk_manager_{nullptr};
    BufferPoolManager* buffer_pool_manager_{nullptr};
    RmManager* rm_manager_{nullptr};
    IxManager* ix_manager_{nullptr};
    SmManager* sm_manager_{nullptr};
    Analyze* analyzer_{nullptr};
    Context* context_{nullptr}; // SmManager methods require Context

    void SetUp() override {
        // Clean up any previous test database files
        if (std::filesystem::exists(TEST_DB_NAME)) {
            std::filesystem::remove_all(TEST_DB_NAME);
        }
        if (std::filesystem::exists(TEST_DB_NAME + ".meta")) {
             std::filesystem::remove(TEST_DB_NAME + ".meta");
        }

        disk_manager_ = new DiskManager();
        buffer_pool_manager_ = new BufferPoolManager(TEST_BUFFER_POOL_SIZE, disk_manager_);
        rm_manager_ = new RmManager(disk_manager_, buffer_pool_manager_);
        ix_manager_ = new IxManager(disk_manager_, buffer_pool_manager_);
        sm_manager_ = new SmManager(disk_manager_, buffer_pool_manager_, rm_manager_, ix_manager_);
        
        // Initialize Context with nullptrs for managers not directly used by these tests,
        // or if SmManager's DDL operations can handle null context for these aspects.
        // The constructor is: Context(LockManager*, LogManager*, Transaction*, char*, int*)
        LockManager* mock_lock_mgr = nullptr;
        LogManager* mock_log_mgr = nullptr;
        Transaction* mock_txn = nullptr;
        context_ = new Context(mock_lock_mgr, mock_log_mgr, mock_txn);


        sm_manager_->create_db(TEST_DB_NAME);
        sm_manager_->open_db(TEST_DB_NAME);

        // Create table t1 (id INT, name CHAR(10))
        std::vector<ColDef> t1_cols = {
            {"id", TYPE_INT, sizeof(int)},
            {"name", TYPE_STRING, 10}
        };
        sm_manager_->create_table("t1", t1_cols, context_);

        // Create table t2 (id INT, fk INT, value CHAR(20))
        std::vector<ColDef> t2_cols = {
            {"id", TYPE_INT, sizeof(int)},
            {"fk", TYPE_INT, sizeof(int)},
            {"value", TYPE_STRING, 20}
        };
        sm_manager_->create_table("t2", t2_cols, context_);
        
        // Create table t3 (id INT, name_t3 CHAR(10)) - an unrelated table
        std::vector<ColDef> t3_cols = {
            {"id", TYPE_INT, sizeof(int)},
            {"name_t3", TYPE_STRING, 10}
        };
        sm_manager_->create_table("t3", t3_cols, context_);


        analyzer_ = new Analyze(sm_manager_);
    }

    void TearDown() override {
        if (sm_manager_) {
            sm_manager_->close_db(); // This should flush meta
        }
        
        delete context_;
        delete analyzer_;
        delete sm_manager_;
        delete ix_manager_;
        delete rm_manager_;
        delete buffer_pool_manager_;
        delete disk_manager_;

        // Clean up database files
        if (std::filesystem::exists(TEST_DB_NAME)) {
            std::filesystem::remove_all(TEST_DB_NAME);
        }
         if (std::filesystem::exists(TEST_DB_NAME + ".meta")) {
             std::filesystem::remove(TEST_DB_NAME + ".meta");
        }
    }
};

// Test Cases

TEST_F(SemiJoinAnalyzerTest, ValidSelectFromLeftTable) {
    std::string sql = "SELECT t1.id, name FROM t1 SEMI JOIN t2 ON t1.id = t2.fk;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    std::shared_ptr<Query> query_analyzed;
    ASSERT_NO_THROW(query_analyzed = analyzer_->do_analyze(ast_root));
    ASSERT_NE(query_analyzed, nullptr);
    ASSERT_EQ(query_analyzed->cols.size(), 2);
    EXPECT_EQ(query_analyzed->cols[0].tab_name, "t1");
    EXPECT_EQ(query_analyzed->cols[0].col_name, "id");
    EXPECT_EQ(query_analyzed->cols[1].tab_name, "t1"); // 'name' should be inferred to t1.name
    EXPECT_EQ(query_analyzed->cols[1].col_name, "name");
}

TEST_F(SemiJoinAnalyzerTest, SelectStar) {
    std::string sql = "SELECT * FROM t1 SEMI JOIN t2 ON t1.id = t2.fk;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    std::shared_ptr<Query> query_analyzed;
    ASSERT_NO_THROW(query_analyzed = analyzer_->do_analyze(ast_root));
    ASSERT_NE(query_analyzed, nullptr);
    // Should select all columns from t1: id, name
    ASSERT_EQ(query_analyzed->cols.size(), 2);
    bool id_found = false, name_found = false;
    for(const auto& col : query_analyzed->cols) {
        EXPECT_EQ(col.tab_name, "t1");
        if(col.col_name == "id") id_found = true;
        if(col.col_name == "name") name_found = true;
    }
    EXPECT_TRUE(id_found);
    EXPECT_TRUE(name_found);
}


TEST_F(SemiJoinAnalyzerTest, SelectFromRightTableFails) {
    std::string sql = "SELECT t2.value FROM t1 SEMI JOIN t2 ON t1.id = t2.fk;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    EXPECT_THROW(analyzer_->do_analyze(ast_root), InvalidColumnError);
}

TEST_F(SemiJoinAnalyzerTest, SelectUnqualifiedColFromRightTableFails) {
    // 'value' only exists in t2, analyzer should detect it's not from t1
    std::string sql = "SELECT value FROM t1 SEMI JOIN t2 ON t1.id = t2.fk;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    // This should be ColumnNotFoundError because 'value' is not in t1's columns (the context for select list)
    EXPECT_THROW(analyzer_->do_analyze(ast_root), ColumnNotFoundError);
}


TEST_F(SemiJoinAnalyzerTest, SelectNonExistentColFromLeftTableFails) {
    std::string sql = "SELECT t1.non_existent FROM t1 SEMI JOIN t2 ON t1.id = t2.fk;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    EXPECT_THROW(analyzer_->do_analyze(ast_root), ColumnNotFoundError);
}

TEST_F(SemiJoinAnalyzerTest, LeftTableNotExistsFails) {
    std::string sql = "SELECT t_non_exist.id FROM t_non_exist SEMI JOIN t2 ON t_non_exist.id = t2.fk;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    EXPECT_THROW(analyzer_->do_analyze(ast_root), TableNotFoundError);
}

TEST_F(SemiJoinAnalyzerTest, RightTableNotExistsFails) {
    std::string sql = "SELECT t1.id FROM t1 SEMI JOIN t_non_exist ON t1.id = t_non_exist.fk;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    EXPECT_THROW(analyzer_->do_analyze(ast_root), TableNotFoundError);
}

TEST_F(SemiJoinAnalyzerTest, OnClauseColFromWrongTableFails) {
    // t1.value does not exist, t2.name does not exist
    std::string sql = "SELECT t1.id FROM t1 SEMI JOIN t2 ON t1.value = t2.name;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    EXPECT_THROW(analyzer_->do_analyze(ast_root), ColumnNotFoundError);
}

TEST_F(SemiJoinAnalyzerTest, OnClauseTypeMismatchFails) {
    // t1.id (INT) vs t2.value (STRING)
    std::string sql = "SELECT t1.id FROM t1 SEMI JOIN t2 ON t1.id = t2.value;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    EXPECT_THROW(analyzer_->do_analyze(ast_root), IncompatibleTypeError);
}

TEST_F(SemiJoinAnalyzerTest, ValidOnClause) {
    std::string sql = "SELECT t1.id FROM t1 SEMI JOIN t2 ON t1.id = t2.fk;"; // fk is INT
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    EXPECT_NO_THROW(analyzer_->do_analyze(ast_root));
}

TEST_F(SemiJoinAnalyzerTest, OuterWhereRefersToRightTableFails) {
    std::string sql = "SELECT t1.id FROM t1 SEMI JOIN t2 ON t1.id = t2.fk WHERE t2.id > 0;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    EXPECT_THROW(analyzer_->do_analyze(ast_root), ColumnNotFoundError); // t2.id not in context {t1}
}

TEST_F(SemiJoinAnalyzerTest, ValidOuterWhere) {
    std::string sql = "SELECT t1.id FROM t1 SEMI JOIN t2 ON t1.id = t2.fk WHERE t1.name = 'test';";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    EXPECT_NO_THROW(analyzer_->do_analyze(ast_root));
}

TEST_F(SemiJoinAnalyzerTest, SelectFromOtherTableFails) {
    std::string sql = "SELECT t3.id FROM t1 SEMI JOIN t2 ON t1.id = t2.fk;";
    auto ast_root = parse_sql_for_analyzer_test(sql);
    ASSERT_NE(ast_root, nullptr);
    // t3.id is not from t1 (the left table of SEMI JOIN)
    EXPECT_THROW(analyzer_->do_analyze(ast_root), InvalidColumnError);
}