/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include <gtest/gtest.h>
#include <memory>
#include <vector>
#include <thread>
#include <chrono>
#include <string>
#include <iostream>
#include <cstring>

#include "transaction/undo_log_manager.h"
#include "transaction/mvcc_helper.h"
#include "transaction/transaction_manager.h"
#include "record/rm_defs.h"
#include "common/config.h"

/**
 * @brief MVCC集成测试套件
 */
class MVCCIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化撤销日志管理器
        undo_mgr = std::make_unique<UndoLogManager>();
        
        // 创建测试用的事务管理器
        txn_mgr = std::make_unique<TransactionManager>(nullptr, nullptr, ConcurrencyMode::MVCC);
    }

    void TearDown() override {
        // 清理资源
        undo_mgr.reset();
        txn_mgr.reset();
    }

    std::unique_ptr<UndoLogManager> undo_mgr;
    std::unique_ptr<TransactionManager> txn_mgr;
};

/**
 * @brief 测试基本的版本链构建
 */
TEST_F(MVCCIntegrationTest, VersionChainTest) {
    // 模拟一个表记录的多个版本
    const char* version1_data = "Version 1: Original Data";
    const char* version2_data = "Version 2: Updated Data";
    const char* version3_data = "Version 3: Final Data";
    
    // 创建第一个版本（原始数据）
    RmRecord current_record(strlen(version3_data) + 1, const_cast<char*>(version3_data));
    
    // 创建版本链：V1 <- V2 <- V3 (current)
    undo_ptr_t undo_ptr1 = undo_mgr->CreateUndoLog(
        UndoLogType::UPDATE, 1, INVALID_UNDO_PTR,
        strlen(version1_data) + 1, version1_data
    );
    
    undo_ptr_t undo_ptr2 = undo_mgr->CreateUndoLog(
        UndoLogType::UPDATE, 2, undo_ptr1,
        strlen(version2_data) + 1, version2_data
    );
    
    // 当前版本的元数据，指向版本2
    TupleMeta current_meta(300, false, 3, undo_ptr2);
    
    // 验证版本链的完整性
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), 2);
    
    auto log2 = undo_mgr->GetUndoLog(undo_ptr2);
    ASSERT_NE(log2, nullptr);
    EXPECT_EQ(log2->header_.creator_txn_id_, 2);
    EXPECT_EQ(log2->header_.prev_undo_ptr_, undo_ptr1);
    EXPECT_STREQ(log2->data_, version2_data);
    
    auto log1 = undo_mgr->GetUndoLog(undo_ptr1);
    ASSERT_NE(log1, nullptr);
    EXPECT_EQ(log1->header_.creator_txn_id_, 1);
    EXPECT_EQ(log1->header_.prev_undo_ptr_, INVALID_UNDO_PTR);
    EXPECT_STREQ(log1->data_, version1_data);
}

/**
 * @brief 测试多事务环境下的快照隔离
 */
TEST_F(MVCCIntegrationTest, SnapshotIsolationTest) {
    // 创建三个不同时间点的事务
    auto txn1 = std::make_unique<Transaction>(1);  // 读时间戳 = 10
    auto txn2 = std::make_unique<Transaction>(2);  // 读时间戳 = 20  
    auto txn3 = std::make_unique<Transaction>(3);  // 读时间戳 = 30
    
    txn1->set_start_ts(10);
    txn2->set_start_ts(20);
    txn3->set_start_ts(30);
    
    // 模拟提交时间戳
    // txn1在时间15提交，txn2在时间25提交，txn3还未提交
    
    // 创建由不同事务创建的元组
    TupleMeta meta_by_txn1(100, false, txn1->get_transaction_id(), INVALID_UNDO_PTR);
    TupleMeta meta_by_txn2(200, false, txn2->get_transaction_id(), INVALID_UNDO_PTR);
    TupleMeta meta_by_txn3(300, false, txn3->get_transaction_id(), INVALID_UNDO_PTR);
    
    // 每个事务都应该能看到自己创建的元组
    EXPECT_TRUE(MVCCHelper::IsVisible(meta_by_txn1, txn1.get(), txn_mgr.get()));
    EXPECT_TRUE(MVCCHelper::IsVisible(meta_by_txn2, txn2.get(), txn_mgr.get()));
    EXPECT_TRUE(MVCCHelper::IsVisible(meta_by_txn3, txn3.get(), txn_mgr.get()));
    
    // 在当前测试环境中，由于所有事务都不在活跃事务表中，
    // 都被认为是已提交的，所以互相可见
    EXPECT_TRUE(MVCCHelper::IsVisible(meta_by_txn1, txn2.get(), txn_mgr.get()));
    EXPECT_TRUE(MVCCHelper::IsVisible(meta_by_txn2, txn1.get(), txn_mgr.get()));
}

/**
 * @brief 测试写入冲突检测
 */
TEST_F(MVCCIntegrationTest, WriteConflictDetectionTest) {
    auto txn1 = std::make_unique<Transaction>(1);
    auto txn2 = std::make_unique<Transaction>(2);
    
    txn1->set_start_ts(10);
    txn2->set_start_ts(20);
    
    // 创建由txn1创建的元组
    TupleMeta meta_by_txn1(100, false, txn1->get_transaction_id(), INVALID_UNDO_PTR);
    
    // txn1对自己创建的元组进行写入 - 应该没有冲突
    EXPECT_TRUE(MVCCHelper::CheckWriteConflict(meta_by_txn1, txn1.get(), txn_mgr.get()));
    
    // txn2尝试写入txn1创建的元组 - 在当前测试环境下也没有冲突
    // 因为txn1不在活跃事务表中，被认为已提交
    EXPECT_TRUE(MVCCHelper::CheckWriteConflict(meta_by_txn1, txn2.get(), txn_mgr.get()));
}

/**
 * @brief 测试删除操作的版本链
 */
TEST_F(MVCCIntegrationTest, DeleteVersionChainTest) {
    const char* original_data = "Original Record Data";
    
    // 创建原始记录
    RmRecord original_record(strlen(original_data) + 1, const_cast<char*>(original_data));
    
    // 创建删除操作的撤销日志
    undo_ptr_t delete_undo_ptr = undo_mgr->CreateUndoLog(
        UndoLogType::DELETE, 1, INVALID_UNDO_PTR,
        strlen(original_data) + 1, original_data
    );
    
    // 当前版本标记为已删除，指向撤销日志
    TupleMeta deleted_meta(200, true, 2, delete_undo_ptr);
    
    // 验证删除的版本链
    auto delete_log = undo_mgr->GetUndoLog(delete_undo_ptr);
    ASSERT_NE(delete_log, nullptr);
    EXPECT_EQ(delete_log->header_.log_type_, UndoLogType::DELETE);
    EXPECT_STREQ(delete_log->data_, original_data);
    
    // 测试事务能否看到被删除的记录
    auto txn = std::make_unique<Transaction>(3);
    txn->set_start_ts(300);
    
    RmRecord visible_record;
    TupleMeta visible_meta;
    
    bool found = MVCCHelper::ReconstructTuple(
        original_record, deleted_meta, txn.get(), txn_mgr.get(),
        undo_mgr.get(), visible_record, visible_meta
    );
    
    // 在当前测试环境下，由于删除事务被认为已提交，
    // 重构应该能找到可见版本
    EXPECT_TRUE(found);
}

/**
 * @brief 测试复杂的版本链遍历
 */
TEST_F(MVCCIntegrationTest, ComplexVersionChainTraversalTest) {
    // 创建一个长版本链：V1 <- V2 <- V3 <- V4 <- V5 (current)
    std::vector<std::string> versions = {
        "Version 1 Data",
        "Version 2 Data", 
        "Version 3 Data",
        "Version 4 Data",
        "Version 5 Data"
    };
    
    std::vector<undo_ptr_t> undo_ptrs;
    
    // 创建版本链
    for (size_t i = 0; i < versions.size() - 1; ++i) {
        undo_ptr_t prev_ptr = (i == 0) ? INVALID_UNDO_PTR : undo_ptrs[i-1];
        undo_ptr_t ptr = undo_mgr->CreateUndoLog(
            UndoLogType::UPDATE, i + 1, prev_ptr,
            versions[i].length() + 1, versions[i].c_str()
        );
        undo_ptrs.push_back(ptr);
    }
    
    // 当前版本
    RmRecord current_record(versions.back().length() + 1, 
                           const_cast<char*>(versions.back().c_str()));
    TupleMeta current_meta(500, false, versions.size(), undo_ptrs.back());
    
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), versions.size() - 1);
    
    // 验证版本链的连通性
    undo_ptr_t current_ptr = undo_ptrs.back();
    for (int i = undo_ptrs.size() - 1; i >= 0; --i) {
        auto log = undo_mgr->GetUndoLog(current_ptr);
        ASSERT_NE(log, nullptr);
        EXPECT_STREQ(log->data_, versions[i].c_str());
        current_ptr = log->header_.prev_undo_ptr_;
    }
    
    EXPECT_EQ(current_ptr, INVALID_UNDO_PTR);
}

/**
 * @brief 测试内存管理和清理
 */
TEST_F(MVCCIntegrationTest, MemoryManagementTest) {
    const int NUM_TRANSACTIONS = 10;
    const int LOGS_PER_TXN = 5;
    
    // 为多个事务创建撤销日志
    for (int txn_id = 1; txn_id <= NUM_TRANSACTIONS; ++txn_id) {
        for (int log_id = 1; log_id <= LOGS_PER_TXN; ++log_id) {
            std::string data = "Txn" + std::to_string(txn_id) + 
                              "_Log" + std::to_string(log_id) + "_Data";
            
            undo_mgr->CreateUndoLog(
                (log_id % 2 == 0) ? UndoLogType::UPDATE : UndoLogType::DELETE,
                txn_id, INVALID_UNDO_PTR,
                data.length() + 1, data.c_str()
            );
        }
    }
    
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), NUM_TRANSACTIONS * LOGS_PER_TXN);
    
    // 清理一半的事务
    for (int txn_id = 1; txn_id <= NUM_TRANSACTIONS / 2; ++txn_id) {
        undo_mgr->CleanupTransactionUndoLogs(txn_id);
    }
    
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), (NUM_TRANSACTIONS / 2) * LOGS_PER_TXN);
    
    // 清理所有日志
    undo_mgr->ClearAllUndoLogs();
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), 0);
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
} 