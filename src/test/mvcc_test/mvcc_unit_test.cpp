/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include <gtest/gtest.h>
#include <memory>
#include <vector>
#include <chrono>
#include <string>
#include <iostream>
#include <cstring>

#include "transaction/undo_log_manager.h"
#include "transaction/mvcc_helper.h"
#include "transaction/transaction_manager.h"
#include "record/rm_defs.h"
#include "common/config.h"

/**
 * @brief MVCC单元测试套件
 */
class MVCCTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化撤销日志管理器
        undo_mgr = std::make_unique<UndoLogManager>();
        
        // 创建测试用的事务管理器
        txn_mgr = std::make_unique<TransactionManager>(nullptr, nullptr, ConcurrencyMode::MVCC);
        
        // 创建测试事务
        test_txn1 = std::make_unique<Transaction>(1);
        test_txn2 = std::make_unique<Transaction>(2);
        test_txn3 = std::make_unique<Transaction>(3);
        
        // 设置测试事务的读时间戳
        test_txn1->set_start_ts(10);
        test_txn2->set_start_ts(20);
        test_txn3->set_start_ts(30);
    }

    void TearDown() override {
        // 清理资源
        undo_mgr.reset();
        txn_mgr.reset();
        test_txn1.reset();
        test_txn2.reset();
        test_txn3.reset();
    }

    std::unique_ptr<UndoLogManager> undo_mgr;
    std::unique_ptr<TransactionManager> txn_mgr;
    std::unique_ptr<Transaction> test_txn1;
    std::unique_ptr<Transaction> test_txn2;
    std::unique_ptr<Transaction> test_txn3;
};

/**
 * @brief 测试TupleMeta结构的基本功能
 */
TEST_F(MVCCTest, TupleMetaBasicTest) {
    // 测试默认构造函数
    TupleMeta meta1;
    EXPECT_EQ(meta1.ts_, 0);
    EXPECT_FALSE(meta1.is_deleted_);
    EXPECT_EQ(meta1.creator_txn_id_, INVALID_TXN_ID);
    EXPECT_EQ(meta1.prev_version_ptr_, INVALID_UNDO_PTR);

    // 测试参数化构造函数
    TupleMeta meta2(100, false, 5, 123);
    EXPECT_EQ(meta2.ts_, 100);
    EXPECT_FALSE(meta2.is_deleted_);
    EXPECT_EQ(meta2.creator_txn_id_, 5);
    EXPECT_EQ(meta2.prev_version_ptr_, 123);

    // 测试相等和不等操作符
    TupleMeta meta3(100, false, 5, 123);
    EXPECT_EQ(meta2, meta3);
    
    TupleMeta meta4(100, true, 5, 123);
    EXPECT_NE(meta2, meta4);
}

/**
 * @brief 测试UndoLogRecord结构的基本功能
 */
TEST_F(MVCCTest, UndoLogRecordBasicTest) {
    // 创建测试数据
    const char* test_data = "Hello MVCC World!";
    int data_size = strlen(test_data) + 1;
    
    // 测试构造函数
    UndoLogRecord undo_log(UndoLogType::UPDATE, 123, 456, data_size, test_data);
    
    EXPECT_EQ(undo_log.header_.log_type_, UndoLogType::UPDATE);
    EXPECT_EQ(undo_log.header_.creator_txn_id_, 123);
    EXPECT_EQ(undo_log.header_.prev_undo_ptr_, 456);
    EXPECT_EQ(undo_log.header_.record_size_, data_size);
    EXPECT_TRUE(undo_log.allocated_);
    EXPECT_STREQ(undo_log.data_, test_data);

    // 测试拷贝构造函数
    UndoLogRecord undo_log_copy(undo_log);
    EXPECT_EQ(undo_log_copy.header_.log_type_, UndoLogType::UPDATE);
    EXPECT_EQ(undo_log_copy.header_.creator_txn_id_, 123);
    EXPECT_EQ(undo_log_copy.header_.prev_undo_ptr_, 456);
    EXPECT_EQ(undo_log_copy.header_.record_size_, data_size);
    EXPECT_TRUE(undo_log_copy.allocated_);
    EXPECT_STREQ(undo_log_copy.data_, test_data);
}

/**
 * @brief 测试撤销日志管理器的基本功能
 */
TEST_F(MVCCTest, UndoLogManagerBasicTest) {
    ASSERT_NE(undo_mgr, nullptr);

    // 初始状态检查
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), 0);

    // 创建第一个撤销日志
    const char* data1 = "Original Data 1";
    undo_ptr_t ptr1 = undo_mgr->CreateUndoLog(
        UndoLogType::UPDATE, 100, INVALID_UNDO_PTR, 
        strlen(data1) + 1, data1
    );
    
    EXPECT_NE(ptr1, INVALID_UNDO_PTR);
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), 1);

    // 获取撤销日志
    auto retrieved_log = undo_mgr->GetUndoLog(ptr1);
    ASSERT_NE(retrieved_log, nullptr);
    EXPECT_EQ(retrieved_log->header_.log_type_, UndoLogType::UPDATE);
    EXPECT_EQ(retrieved_log->header_.creator_txn_id_, 100);
    EXPECT_EQ(retrieved_log->header_.prev_undo_ptr_, INVALID_UNDO_PTR);
    EXPECT_STREQ(retrieved_log->data_, data1);

    // 创建第二个撤销日志，链接到第一个
    const char* data2 = "Updated Data 2";
    undo_ptr_t ptr2 = undo_mgr->CreateUndoLog(
        UndoLogType::UPDATE, 101, ptr1, 
        strlen(data2) + 1, data2
    );
    
    EXPECT_NE(ptr2, INVALID_UNDO_PTR);
    EXPECT_NE(ptr2, ptr1);
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), 2);

    // 验证版本链
    auto log2 = undo_mgr->GetUndoLog(ptr2);
    ASSERT_NE(log2, nullptr);
    EXPECT_EQ(log2->header_.prev_undo_ptr_, ptr1);
    EXPECT_STREQ(log2->data_, data2);
}

/**
 * @brief 测试撤销日志管理器的清理功能
 */
TEST_F(MVCCTest, UndoLogManagerCleanupTest) {
    // 为不同事务创建撤销日志
    txn_id_t txn1_id = 100;
    txn_id_t txn2_id = 200;

    const char* data1 = "Transaction 1 Data";
    const char* data2 = "Transaction 2 Data";
    
    undo_ptr_t ptr1 = undo_mgr->CreateUndoLog(
        UndoLogType::UPDATE, txn1_id, INVALID_UNDO_PTR,
        strlen(data1) + 1, data1
    );
    
    undo_ptr_t ptr2 = undo_mgr->CreateUndoLog(
        UndoLogType::DELETE, txn2_id, INVALID_UNDO_PTR,
        strlen(data2) + 1, data2
    );

    EXPECT_EQ(undo_mgr->GetUndoLogCount(), 2);

    // 清理事务1的撤销日志
    undo_mgr->CleanupTransactionUndoLogs(txn1_id);
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), 1);

    // 验证事务1的日志已被删除，事务2的日志仍存在
    EXPECT_EQ(undo_mgr->GetUndoLog(ptr1), nullptr);
    EXPECT_NE(undo_mgr->GetUndoLog(ptr2), nullptr);

    // 清理所有日志
    undo_mgr->ClearAllUndoLogs();
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), 0);
    EXPECT_EQ(undo_mgr->GetUndoLog(ptr2), nullptr);
}

/**
 * @brief 测试MVCC可见性检查功能
 */
TEST_F(MVCCTest, MVCCVisibilityTest) {
    // 创建测试元组元数据
    TupleMeta meta1(100, false, test_txn1->get_transaction_id(), INVALID_UNDO_PTR);
    TupleMeta meta2(200, false, test_txn2->get_transaction_id(), INVALID_UNDO_PTR);
    
    // 测试自己创建的元组对自己可见
    EXPECT_TRUE(MVCCHelper::IsVisible(meta1, test_txn1.get(), txn_mgr.get()));
    
    // 测试其他事务创建的元组在事务管理器中不存在时的可见性
    // 注意：由于测试环境中事务不在事务管理器的活跃列表中，
    // 会被认为是已提交的事务，因此可见
    EXPECT_TRUE(MVCCHelper::IsVisible(meta2, test_txn1.get(), txn_mgr.get()));
}

/**
 * @brief 测试MVCC写入冲突检查功能
 */
TEST_F(MVCCTest, MVCCWriteConflictTest) {
    // 创建测试元组元数据
    TupleMeta meta1(100, false, test_txn1->get_transaction_id(), INVALID_UNDO_PTR);
    TupleMeta meta2(200, false, test_txn2->get_transaction_id(), INVALID_UNDO_PTR);
    
    // 测试自己创建的元组没有写入冲突
    EXPECT_TRUE(MVCCHelper::CheckWriteConflict(meta1, test_txn1.get(), txn_mgr.get()));
    
    // 测试其他事务创建的元组在事务管理器中不存在时没有写入冲突
    EXPECT_TRUE(MVCCHelper::CheckWriteConflict(meta2, test_txn1.get(), txn_mgr.get()));
}

/**
 * @brief 测试元组重构功能
 */
TEST_F(MVCCTest, TupleReconstructionTest) {
    // 创建测试记录
    const char* current_data = "Current Version Data";
    const char* old_data = "Old Version Data";
    
    RmRecord current_record(strlen(current_data) + 1, const_cast<char*>(current_data));
    
    // 创建撤销日志
    undo_ptr_t undo_ptr = undo_mgr->CreateUndoLog(
        UndoLogType::UPDATE, test_txn1->get_transaction_id(), INVALID_UNDO_PTR,
        strlen(old_data) + 1, old_data
    );
    
    // 创建当前版本的元数据，指向撤销日志
    TupleMeta current_meta(200, false, test_txn2->get_transaction_id(), undo_ptr);
    
    // 尝试重构元组
    RmRecord visible_record;
    TupleMeta visible_meta;
    
    bool success = MVCCHelper::ReconstructTuple(
        current_record, current_meta, test_txn1.get(), txn_mgr.get(),
        undo_mgr.get(), visible_record, visible_meta
    );
    
    // 在当前测试环境中，由于所有事务都被认为是已提交的，
    // 当前版本应该是可见的
    EXPECT_TRUE(success);
}

/**
 * @brief 测试撤销日志重构功能
 */
TEST_F(MVCCTest, UndoLogReconstructionTest) {
    const char* test_data = "Test Reconstruction Data";
    
    // 创建撤销日志
    UndoLogRecord undo_log(
        UndoLogType::UPDATE, 123, INVALID_UNDO_PTR, 
        strlen(test_data) + 1, test_data
    );
    
    // 从撤销日志重构记录
    RmRecord reconstructed_record;
    bool success = MVCCHelper::ReconstructFromUndoLog(undo_log, reconstructed_record);
    
    EXPECT_TRUE(success);
    EXPECT_EQ(reconstructed_record.size, strlen(test_data) + 1);
    EXPECT_STREQ(reconstructed_record.data, test_data);
}

/**
 * @brief 测试边界情况和错误处理
 */
TEST_F(MVCCTest, EdgeCasesTest) {
    // 测试获取不存在的撤销日志
    EXPECT_EQ(undo_mgr->GetUndoLog(INVALID_UNDO_PTR), nullptr);
    EXPECT_EQ(undo_mgr->GetUndoLog(999999), nullptr);
    
    // 测试空数据的撤销日志
    undo_ptr_t empty_ptr = undo_mgr->CreateUndoLog(
        UndoLogType::DELETE, 100, INVALID_UNDO_PTR, 0, nullptr
    );
    
    auto empty_log = undo_mgr->GetUndoLog(empty_ptr);
    ASSERT_NE(empty_log, nullptr);
    EXPECT_EQ(empty_log->header_.record_size_, 0);
    EXPECT_EQ(empty_log->data_, nullptr);
    
    // 测试空指针的可见性检查
    TupleMeta test_meta(100, false, 123, INVALID_UNDO_PTR);
    EXPECT_FALSE(MVCCHelper::IsVisible(test_meta, nullptr, txn_mgr.get()));
    EXPECT_FALSE(MVCCHelper::CheckWriteConflict(test_meta, nullptr, txn_mgr.get()));
    
    // 测试空撤销日志的重构
    UndoLogRecord invalid_undo_log(UndoLogType::UPDATE, 123, INVALID_UNDO_PTR, 0, nullptr);
    RmRecord invalid_record;
    EXPECT_FALSE(MVCCHelper::ReconstructFromUndoLog(invalid_undo_log, invalid_record));
}

/**
 * @brief 性能测试 - 测试大量撤销日志的创建和查询
 */
TEST_F(MVCCTest, PerformanceTest) {
    const int NUM_LOGS = 1000;
    std::vector<undo_ptr_t> undo_ptrs;
    
    // 创建大量撤销日志
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < NUM_LOGS; ++i) {
        std::string data = "Performance Test Data " + std::to_string(i);
        undo_ptr_t ptr = undo_mgr->CreateUndoLog(
            UndoLogType::UPDATE, i % 10, INVALID_UNDO_PTR,
            data.length() + 1, data.c_str()
        );
        undo_ptrs.push_back(ptr);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(undo_mgr->GetUndoLogCount(), NUM_LOGS);
    std::cout << "Created " << NUM_LOGS << " undo logs in " << duration.count() << " ms" << std::endl;
    
    // 测试查询性能
    start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < NUM_LOGS; ++i) {
        auto log = undo_mgr->GetUndoLog(undo_ptrs[i]);
        EXPECT_NE(log, nullptr);
    }
    
    end_time = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "Queried " << NUM_LOGS << " undo logs in " << duration.count() << " ms" << std::endl;
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
} 