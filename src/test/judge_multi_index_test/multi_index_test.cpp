#include <gtest/gtest.h>
#include <iostream>
#include <string>
#include <vector>
#include <cstdio>
#include <cstdlib>
#include <fstream>
#include <unistd.h>
#include <sys/wait.h>
#include <sstream>
#include <algorithm>
#include <random>
#include <chrono>
#include <cmath>

#define TEST_DB_NAME "test_multi_index"
#define SQL_FILE "test_multi_index.sql"
#define OUTPUT_FILE "./test_multi_index/output.txt"
#define REMOVE 1
#define SELECT 1

struct WarehouseRecord {
    int w_id;
    std::string name;
    float flo;
};

// Helper to read entire file for logging
std::string get_file_contents(const char* filename) {
    std::ifstream in(filename, std::ios::in | std::ios::binary);
    if (in) {
        std::ostringstream contents;
        contents << in.rdbuf();
        in.close();
        return contents.str();
    }
    return "Error reading file: " + std::string(filename);
}

class MultiIndexTest : public ::testing::Test {
protected:
    pid_t server_pid_ = -1;

    void SetUp() override {
        // Clean up previous test files
        if (REMOVE){
        remove(TEST_DB_NAME);
        remove(SQL_FILE);
        remove(OUTPUT_FILE);
        }
        server_pid_ = fork();
        if (server_pid_ == 0) { // Child process
            // Start the database server
            execl("./bin/rmdb", "rmdb", TEST_DB_NAME, (char*)NULL);
            exit(127); // execl error
        }
        // Give server time to start
        sleep(1);
    }

    void TearDown() override {
        if (server_pid_ > 0) {
            kill(server_pid_, SIGINT);
            int status;
            waitpid(server_pid_, &status, 0);
        }
        // Clean up after test
        if (REMOVE){
        remove(TEST_DB_NAME);
        remove(SQL_FILE);
        remove(OUTPUT_FILE);
        }
    }

    void RunSQL(const std::string& sql) {
        if (REMOVE){
        remove(OUTPUT_FILE); // Explicitly clear previous output
        }
        std::ofstream sql_file(SQL_FILE);
        sql_file << sql;
        sql_file.close();

        std::string command = "../rmdb_client/build/rmdb_client " + std::string(TEST_DB_NAME) + " < " + SQL_FILE ;
        system(command.c_str());
    }

    bool FindRecordInOutput(const WarehouseRecord& target_rec) {
        // std::cout << "Finding record in output file: " << OUTPUT_FILE << std::endl;
        std::ifstream out_file(OUTPUT_FILE);
        std::string line;
        bool found = false;
        while(std::getline(out_file, line)) {
            if (line.rfind('|', 0) == 0) { // Check if it's a data row
                std::stringstream ss(line);
                std::string segment;
                std::vector<std::string> seglist;
                while(std::getline(ss, segment, '|')) {
                   seglist.push_back(segment);
                }

                if (seglist.size() < 4) continue; // Must have at least | w_id | name | flo |

                // 1. Validate w_id
                std::string w_id_str = seglist[1];
                size_t first = w_id_str.find_first_not_of(" \t\n\r");
                if (std::string::npos == first) continue;
                size_t last = w_id_str.find_last_not_of(" \t\n\r");
                std::string trimmed_w_id_str = w_id_str.substr(first, (last - first + 1));
                
                int w_id_val = -1;
                try { w_id_val = std::stoi(trimmed_w_id_str); } catch(...) { continue; }
                if (w_id_val != target_rec.w_id) continue;

                // 2. Validate name
                std::string name_str = seglist[2];
                first = name_str.find_first_not_of(" \t\n\r");
                if (std::string::npos == first) continue;
                last = name_str.find_last_not_of(" \t\n\r");
                std::string trimmed_name = name_str.substr(first, (last - first + 1));

                std::string target_name = target_rec.name;
                if (target_name.length() >= 2 && target_name.front() == '\'' && target_name.back() == '\'') {
                    target_name = target_name.substr(1, target_name.length() - 2);
                }
                if (trimmed_name != target_name) continue;

                // 3. Validate flo
                std::string flo_str = seglist[3];
                first = flo_str.find_first_not_of(" \t\n\r");
                if (std::string::npos == first) continue;
                last = flo_str.find_last_not_of(" \t\n\r");
                std::string trimmed_flo_str = flo_str.substr(first, (last - first + 1));
                
                float flo_val = 0.0f;
                try { flo_val = std::stof(trimmed_flo_str); } catch(...) { continue; }
                if (std::fabs(flo_val - target_rec.flo) > 1e-6) continue;

                // If all checks pass, we found the record.
                found = true;
                break;
            }
        }
        out_file.close();
        return found;
    }
};

TEST_F(MultiIndexTest, JudgeWhetherUseIndexOnMultipleAttributes) {
    std::vector<WarehouseRecord> records;
    const int num_records = 3000;

    // --- NEW DATA GENERATION ---
    // This new generation scheme mimics the key properties of the OJ test:
    // 1. Uses floats that are exactly representable in binary (integer + 0.5).
    // 2. Creates an inverse correlation between w_id and flo, which tests
    //    the composite key sorting in the B+ Tree. (w_id increases, flo decreases).
    for (int i = 1; i <= num_records; ++i) {
        int w_id = i;
        // Generate a simple, clean, inversely correlated float value.
        // E.g., for w_id=1, flo=4000.5; for w_id=2, flo=3999.5, etc.
        float flo_val = (float)(num_records + 1 - i) + 0.5f; 
        
        // The name column can be simple, but let's stick to CHAR(8) limit.
        std::string name = "'name" + std::to_string(i) + "'";
        if (name.length() > 10) { // 'name' + 'i' + ''
             name = name.substr(0, 7) + "'"; // Truncate to fit CHAR(8)
        }
        records.push_back({w_id, name, flo_val});
    }

    // Shuffle for insertion to test index creation on non-sequential data
    auto rd = std::random_device{};
    auto g = std::mt19937{rd()};
    std::shuffle(records.begin(), records.end(), g);

    // Create table and insert data
    std::string setup_sql;
    setup_sql += "create table warehouse(w_id int,name char(8),flo float);\n";
    for (const auto& rec : records) {
        setup_sql += "insert into warehouse values(" +
                     std::to_string(rec.w_id) + "," +
                     rec.name + "," +
                     std::to_string(rec.flo) + ");\n";
    }
    RunSQL(setup_sql);

    // Sort records back by w_id for deterministic querying
    std::sort(records.begin(), records.end(), [](const auto& a, const auto& b) {
        return a.w_id < b.w_id;
    });

    // --- Time queries BEFORE index ---
    auto start_no_index = std::chrono::high_resolution_clock::now();
    for (const auto& rec : records) {
        // Construct the query using a string representation of the float.
        // This is important. We are simulating a user typing the query.
        std::string query_sql = "select * from warehouse where w_id = " + std::to_string(rec.w_id) + 
                                " and flo = " + std::to_string(rec.flo) + ";\n";
        // RunSQL(query_sql); 
        // ASSERT_TRUE(FindRecordInOutput(rec)); // This part should still pass
    }
    auto end_no_index = std::chrono::high_resolution_clock::now();
    double time_a = std::chrono::duration_cast<std::chrono::milliseconds>(end_no_index - start_no_index).count();
    
    // --- Create index ---
    RunSQL("create index warehouse(w_id,flo);\n");

    // --- Time queries AFTER index ---
    // THIS IS THE PART THAT IS LIKELY TO FAIL NOW
    auto start_with_index = std::chrono::high_resolution_clock::now();
    for (const auto& rec : records) {
        // Use a high-precision string format for the float in the query,
        // similar to the OJ's `1024.500000`. This makes the test more robust.
        std::ostringstream flo_ss;
        flo_ss << std::fixed << std::setprecision(6) << rec.flo;
        std::string flo_str = flo_ss.str();

        std::string query_sql = "select * from warehouse where w_id = " + std::to_string(rec.w_id) + 
                                " and flo = " + flo_str + ";\n";
        RunSQL(query_sql);
        
        // This assertion will now likely fail if your index logic is buggy
        ASSERT_TRUE(FindRecordInOutput(rec)) << "Failed to find record for w_id = " << rec.w_id 
                                             << " and flo = " << flo_str << " AFTER creating index. "
                                             << "This likely indicates a float comparison bug in your B+ Tree index. "
                                             << "Output was:\\n" << get_file_contents(OUTPUT_FILE);
    }
    auto end_with_index = std::chrono::high_resolution_clock::now();
    double time_b = std::chrono::duration_cast<std::chrono::milliseconds>(end_with_index - start_with_index).count();

    // The rest of your performance check logic
    double ratio = (time_a > 0) ? (time_b / time_a) : 0;
    std::cout << "Time without index (time_a): " << time_a << " ms" << std::endl;
    std::cout << "Time with index (time_b): " << time_b << " ms" << std::endl;
    std::cout << "Performance ratio (time_b / time_a): " << ratio * 100 << "%" << std::endl;
    
    ASSERT_LE(ratio, 0.7) << "Query time with index is not significantly faster. "
                          << "Ratio was " << ratio * 100 << "%, expected <= 70%.";
}
TEST_F(MultiIndexTest, CompositeKeySecondPart) {
    // 1. Create table
    RunSQL("create table warehouse(w_id int, name char(8), flo float);");
    
    // 2. Insert records with the SAME w_id but DIFFERENT flo
    RunSQL("insert into warehouse values(100, 'name_A  ', 256.5);");
    RunSQL("insert into warehouse values(100, 'name_B  ', 128.5);");

    // 3. Create index
    RunSQL("create index warehouse(w_id, flo);");

    // 4. Query for the specific records. 
    // If your comparison function ignores `flo`, the B-Tree might only "see"
    // one of the records, or get confused and return the wrong one or none.
    
    // Query for the first record
    RunSQL("select * from warehouse where w_id = 100 and flo = 256.5;");
    ASSERT_TRUE(FindRecordInOutput({100, "'name_A  '", 256.5}));

    // Query for the second record
    RunSQL("select * from warehouse where w_id = 100 and flo = 128.5;");
    ASSERT_TRUE(FindRecordInOutput({100, "'name_B  '", 128.5}));
}