/**
 * @brief MVCC阶段二测试：事务管理器与时间戳
 * 测试全局时间戳分配、事务状态管理和活跃事务跟踪
 */

#include <gtest/gtest.h>
#include <thread>
#include <vector>
#include <chrono>
#include "transaction/transaction_manager.h"
#include "transaction/concurrency/lock_manager.h"
#include <memory>
#include <string>

#include "storage/disk_manager.h"
#include "storage/buffer_pool_manager.h"
#include "record/rm_manager.h"
#include "record/rm_file_handle.h"
#include "transaction/undo_log_manager.h"
#include "transaction/mvcc_helper.h"
#include "common/context.h"

class MVCCStage2Test : public ::testing::Test {
protected:
    void SetUp() override {
        disk_manager_ = std::make_unique<DiskManager>();
        buffer_pool_manager_ = std::make_unique<BufferPoolManager>(100, disk_manager_.get());
        rm_manager_ = std::make_unique<RmManager>(disk_manager_.get(), buffer_pool_manager_.get());
        
        lock_manager_ = std::make_unique<LockManager>();
        log_manager_ = std::make_unique<LogManager>(disk_manager_.get());
        txn_manager_ = std::make_unique<TransactionManager>(lock_manager_.get(), nullptr, ConcurrencyMode::MVCC);
        undo_mgr_ = std::make_unique<UndoLogManager>();
        
        auto test_info = ::testing::UnitTest::GetInstance()->current_test_info();
        test_table_name_ = std::string("test_") + test_info->test_case_name() + "_" + test_info->name();
        user_record_size_ = 64;
        
        try {
            rm_manager_->destroy_file(test_table_name_);
        } catch (const std::exception&) {}
        
        rm_manager_->create_file(test_table_name_, user_record_size_);
        file_handle_ = rm_manager_->open_file(test_table_name_);
    }

    void TearDown() override {
        if (file_handle_) {
            file_handle_.reset();
        }
        try {
            rm_manager_->destroy_file(test_table_name_);
        } catch (const std::exception&) {}
    }

protected:
    std::unique_ptr<DiskManager> disk_manager_;
    std::unique_ptr<BufferPoolManager> buffer_pool_manager_;
    std::unique_ptr<RmManager> rm_manager_;
    std::unique_ptr<LockManager> lock_manager_;
    std::unique_ptr<LogManager> log_manager_;
    std::unique_ptr<TransactionManager> txn_manager_;
    std::unique_ptr<UndoLogManager> undo_mgr_;
    std::unique_ptr<RmFileHandle> file_handle_;
    
    std::string test_table_name_;
    int user_record_size_;
};

// 测试1：时间戳分配的单调递增性
TEST_F(MVCCStage2Test, TimestampAllocation) {
    // 测试时间戳分配是否单调递增
    std::vector<timestamp_t> timestamps;
    for (int i = 0; i < 100; ++i) {
        timestamps.push_back(txn_manager_->allocate_timestamp());
    }
    
    // 验证单调递增
    for (size_t i = 1; i < timestamps.size(); ++i) {
        EXPECT_GT(timestamps[i], timestamps[i-1]) 
            << "时间戳应该单调递增";
    }
}

// 测试2：并发时间戳分配
TEST_F(MVCCStage2Test, ConcurrentTimestampAllocation) {
    const int num_threads = 10;
    const int timestamps_per_thread = 100;
    std::vector<std::vector<timestamp_t>> thread_timestamps(num_threads);
    std::vector<std::thread> threads;
    
    // 启动多个线程并发分配时间戳
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, timestamps_per_thread, &thread_timestamps]() {
            for (int j = 0; j < timestamps_per_thread; ++j) {
                thread_timestamps[i].push_back(txn_manager_->allocate_timestamp());
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    // 收集所有时间戳并排序
    std::vector<timestamp_t> all_timestamps;
    for (const auto& thread_ts : thread_timestamps) {
        all_timestamps.insert(all_timestamps.end(), thread_ts.begin(), thread_ts.end());
    }
    std::sort(all_timestamps.begin(), all_timestamps.end());
    
    // 验证没有重复的时间戳
    for (size_t i = 1; i < all_timestamps.size(); ++i) {
        EXPECT_NE(all_timestamps[i], all_timestamps[i-1]) 
            << "不应该有重复的时间戳";
    }
}

// 测试3：事务开始时的读时间戳设置
TEST_F(MVCCStage2Test, TransactionBeginReadTimestamp) {
    // 模拟一些已提交的事务来设置latest_committed_ts
    txn_manager_->update_latest_committed_ts(100);
    
    // 开始新事务
    auto* txn = txn_manager_->begin(nullptr, nullptr);
    
    EXPECT_EQ(txn->get_read_ts(), 100) 
        << "新事务的读时间戳应该等于最新已提交时间戳";
    
    // 清理
    delete txn;
}

// 测试4：事务提交时的时间戳分配
TEST_F(MVCCStage2Test, TransactionCommitTimestamp) {
    // 开始事务
    auto* txn = txn_manager_->begin(nullptr, nullptr);
    timestamp_t read_ts = txn->get_read_ts();
    
    // 提交事务
    txn_manager_->commit(txn, nullptr);
    
    // 验证提交时间戳
    timestamp_t commit_ts = txn->get_commit_ts();
    EXPECT_GT(commit_ts, read_ts) 
        << "提交时间戳应该大于读时间戳";
    
    EXPECT_EQ(txn_manager_->get_latest_committed_ts(), commit_ts)
        << "全局最新已提交时间戳应该被更新";
    
    // 清理
    delete txn;
}

// 测试5：多个事务的提交时间戳顺序
TEST_F(MVCCStage2Test, MultipleTransactionCommitOrder) {
    std::vector<Transaction*> transactions;
    
    // 开始多个事务
    for (int i = 0; i < 5; ++i) {
        transactions.push_back(txn_manager_->begin(nullptr, nullptr));
    }
    
    // 按顺序提交
    std::vector<timestamp_t> commit_timestamps;
    for (auto* txn : transactions) {
        txn_manager_->commit(txn, nullptr);
        commit_timestamps.push_back(txn->get_commit_ts());
    }
    
    // 验证提交时间戳是单调递增的
    for (size_t i = 1; i < commit_timestamps.size(); ++i) {
        EXPECT_GT(commit_timestamps[i], commit_timestamps[i-1])
            << "提交时间戳应该单调递增";
    }
    
    // 清理
    for (auto* txn : transactions) {
        delete txn;
    }
}

// 测试6：事务中止处理
TEST_F(MVCCStage2Test, TransactionAbort) {
    auto* txn = txn_manager_->begin(nullptr, nullptr);
    txn_id_t txn_id = txn->get_transaction_id();
    
    // 中止事务
    txn_manager_->abort(txn, nullptr);
    
    // 验证事务状态
    EXPECT_EQ(txn->get_state(), TransactionState::ABORTED)
        << "事务状态应该为ABORTED";
    
    // 注意：在MVCC中，事务可能不会立即从全局事务表中移除
    // 这是为了支持对已提交/中止事务的查询
    
    // 清理
    delete txn;
}

// 测试7：最新已提交时间戳的并发更新
TEST_F(MVCCStage2Test, ConcurrentCommitTimestampUpdate) {
    const int num_threads = 10;
    std::vector<std::thread> threads;
    std::vector<timestamp_t> final_timestamps(num_threads);
    
    // 启动多个线程并发更新最新已提交时间戳
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, &final_timestamps]() {
            // 分配时间戳并更新
            timestamp_t ts = txn_manager_->allocate_timestamp();
            txn_manager_->update_latest_committed_ts(ts);
            final_timestamps[i] = ts;
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    // 验证最终的latest_committed_ts是所有时间戳中的最大值
    timestamp_t max_ts = *std::max_element(final_timestamps.begin(), final_timestamps.end());
    EXPECT_EQ(txn_manager_->get_latest_committed_ts(), max_ts)
        << "最新已提交时间戳应该是所有时间戳中的最大值";
}

// 测试8：事务生命周期完整测试
TEST_F(MVCCStage2Test, TransactionLifecycle) {
    // 设置初始状态
    txn_manager_->update_latest_committed_ts(50);
    
    // 开始事务
    auto* txn = txn_manager_->begin(nullptr, nullptr);
    EXPECT_EQ(txn->get_read_ts(), 50);
    EXPECT_EQ(txn->get_state(), TransactionState::DEFAULT);
    
    // 验证事务在全局事务表中
    EXPECT_EQ(txn_manager_->get_transaction(txn->get_transaction_id()), txn);
    
    // 提交事务
    txn_manager_->commit(txn, nullptr);
    EXPECT_EQ(txn->get_state(), TransactionState::COMMITTED);
    EXPECT_GT(txn->get_commit_ts(), txn->get_read_ts());
    
    // 注意：在MVCC中，事务可能不会立即从全局事务表中移除
    // 这是为了支持对已提交/中止事务的查询
    
    // 清理
    delete txn;
}

// 测试1：基本版本链创建 - Update操作
TEST_F(MVCCStage2Test, BasicVersionChainCreation_Update) {
    // 创建第一个事务插入记录
    auto* txn1 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context1 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn1, undo_mgr_.get());
    
    const char* original_data = "Original Version";
    char original_buffer[64];
    memset(original_buffer, 0, 64);
    strncpy(original_buffer, original_data, strlen(original_data));
    
    Rid record_rid = file_handle_->insert_record(original_buffer, context1.get());
    txn_manager_->commit(txn1, log_manager_.get());
    
    // 创建第二个事务更新记录
    auto* txn2 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context2 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn2, undo_mgr_.get());
    
    const char* updated_data = "Updated Version";
    char updated_buffer[64];
    memset(updated_buffer, 0, 64);
    strncpy(updated_buffer, updated_data, strlen(updated_data));
    
    // 更新记录 - 这应该创建撤销日志
    file_handle_->update_record(record_rid, updated_buffer, context2.get());
    
    // 验证撤销日志已创建
    EXPECT_EQ(undo_mgr_->GetUndoLogCount(), 1);
    
    // 获取更新后的记录
    auto updated_record = file_handle_->get_record(record_rid, context2.get());
    TupleMeta updated_meta = file_handle_->extract_tuple_meta(*updated_record);
    
    // 验证版本链指针
    EXPECT_NE(updated_meta.prev_version_ptr_, INVALID_UNDO_PTR);
    EXPECT_EQ(updated_meta.creator_txn_id_, txn2->get_transaction_id());
    
    // 验证撤销日志内容
    auto undo_log = undo_mgr_->GetUndoLog(updated_meta.prev_version_ptr_);
    ASSERT_NE(undo_log, nullptr);
    EXPECT_EQ(undo_log->header_.log_type_, UndoLogType::UPDATE);
    EXPECT_EQ(memcmp(undo_log->data_, original_buffer, strlen(original_data)), 0);
    
    txn_manager_->commit(txn2, log_manager_.get());
    delete txn1;
    delete txn2;
}

// 测试2：基本版本链创建 - Delete操作
TEST_F(MVCCStage2Test, BasicVersionChainCreation_Delete) {
    // 创建第一个事务插入记录
    auto* txn1 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context1 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn1, undo_mgr_.get());
    
    const char* original_data = "To Be Deleted";
    char original_buffer[64];
    memset(original_buffer, 0, 64);
    strncpy(original_buffer, original_data, strlen(original_data));
    
    Rid record_rid = file_handle_->insert_record(original_buffer, context1.get());
    txn_manager_->commit(txn1, log_manager_.get());
    
    // 创建第二个事务删除记录
    auto* txn2 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context2 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn2, undo_mgr_.get());
    
    // 删除记录 - 这应该创建撤销日志
    file_handle_->delete_record(record_rid, context2.get());
    
    // 验证撤销日志已创建
    EXPECT_EQ(undo_mgr_->GetUndoLogCount(), 1);
    
    // 获取删除后的记录
    auto deleted_record = file_handle_->get_record(record_rid, context2.get());
    TupleMeta deleted_meta = file_handle_->extract_tuple_meta(*deleted_record);
    
    // 验证删除标记和版本链
    EXPECT_TRUE(deleted_meta.is_deleted_);
    EXPECT_NE(deleted_meta.prev_version_ptr_, INVALID_UNDO_PTR);
    EXPECT_EQ(deleted_meta.creator_txn_id_, txn2->get_transaction_id());
    
    // 验证撤销日志内容
    auto undo_log = undo_mgr_->GetUndoLog(deleted_meta.prev_version_ptr_);
    ASSERT_NE(undo_log, nullptr);
    EXPECT_EQ(undo_log->header_.log_type_, UndoLogType::DELETE);
    EXPECT_EQ(memcmp(undo_log->data_, original_buffer, strlen(original_data)), 0);
    
    txn_manager_->commit(txn2, log_manager_.get());
    delete txn1;
    delete txn2;
}

// 测试3：复杂版本链 - 多次更新
TEST_F(MVCCStage2Test, ComplexVersionChain_MultipleUpdates) {
    // 创建初始记录
    auto* txn1 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context1 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn1, undo_mgr_.get());
    
    const char* version1 = "Version 1";
    char buffer1[64];
    memset(buffer1, 0, 64);
    strncpy(buffer1, version1, strlen(version1));
    
    Rid record_rid = file_handle_->insert_record(buffer1, context1.get());
    txn_manager_->commit(txn1, log_manager_.get());
    
    // 第一次更新
    auto* txn2 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context2 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn2, undo_mgr_.get());
    
    const char* version2 = "Version 2";
    char buffer2[64];
    memset(buffer2, 0, 64);
    strncpy(buffer2, version2, strlen(version2));
    
    file_handle_->update_record(record_rid, buffer2, context2.get());
    txn_manager_->commit(txn2, log_manager_.get());
    
    // 第二次更新
    auto* txn3 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context3 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn3, undo_mgr_.get());
    
    const char* version3 = "Version 3";
    char buffer3[64];
    memset(buffer3, 0, 64);
    strncpy(buffer3, version3, strlen(version3));
    
    file_handle_->update_record(record_rid, buffer3, context3.get());
    txn_manager_->commit(txn3, log_manager_.get());
    
    // 验证有2个撤销日志（两次更新）
    EXPECT_EQ(undo_mgr_->GetUndoLogCount(), 2);
    
    // 获取最终记录
    auto final_record = file_handle_->get_record(record_rid, context3.get());
    TupleMeta final_meta = file_handle_->extract_tuple_meta(*final_record);
    
    // 验证版本链：Version3 -> Version2 -> Version1
    EXPECT_NE(final_meta.prev_version_ptr_, INVALID_UNDO_PTR);
    
    // 检查第一个撤销日志（Version2的数据）
    auto undo_log1 = undo_mgr_->GetUndoLog(final_meta.prev_version_ptr_);
    ASSERT_NE(undo_log1, nullptr);
    EXPECT_EQ(memcmp(undo_log1->data_, buffer2, strlen(version2)), 0);
    EXPECT_NE(undo_log1->header_.prev_undo_ptr_, INVALID_UNDO_PTR);
    
    // 检查第二个撤销日志（Version1的数据）
    auto undo_log2 = undo_mgr_->GetUndoLog(undo_log1->header_.prev_undo_ptr_);
    ASSERT_NE(undo_log2, nullptr);
    EXPECT_EQ(memcmp(undo_log2->data_, buffer1, strlen(version1)), 0);
    EXPECT_EQ(undo_log2->header_.prev_undo_ptr_, INVALID_UNDO_PTR);
    
    delete txn1;
    delete txn2;
    delete txn3;
}

// 测试4：版本链重构算法
TEST_F(MVCCStage2Test, VersionChainReconstruction) {
    // 创建版本链：V1 -> V2 -> V3（当前）
    auto* txn1 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context1 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn1, undo_mgr_.get());
    
    const char* version1 = "Version 1 Data";
    char buffer1[64];
    memset(buffer1, 0, 64);
    strncpy(buffer1, version1, strlen(version1));
    
    Rid record_rid = file_handle_->insert_record(buffer1, context1.get());
    txn_manager_->commit(txn1, log_manager_.get());
    
    // 更新到Version 2
    auto* txn2 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context2 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn2, undo_mgr_.get());
    
    const char* version2 = "Version 2 Data";
    char buffer2[64];
    memset(buffer2, 0, 64);
    strncpy(buffer2, version2, strlen(version2));
    
    file_handle_->update_record(record_rid, buffer2, context2.get());
    txn_manager_->commit(txn2, log_manager_.get());
    
    // 更新到Version 3
    auto* txn3 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context3 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn3, undo_mgr_.get());
    
    const char* version3 = "Version 3 Data";
    char buffer3[64];
    memset(buffer3, 0, 64);
    strncpy(buffer3, version3, strlen(version3));
    
    file_handle_->update_record(record_rid, buffer3, context3.get());
    txn_manager_->commit(txn3, log_manager_.get());
    
    // 创建一个新事务来测试版本重构
    auto* txn4 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context4 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn4, undo_mgr_.get());
    
    // 获取当前记录
    auto current_record = file_handle_->get_record(record_rid, context4.get());
    TupleMeta current_meta = file_handle_->extract_tuple_meta(*current_record);
    
    // 测试版本重构 - 应该能看到最新版本
    RmRecord visible_record;
    TupleMeta visible_meta;
    
    bool found = MVCCHelper::ReconstructTuple(
        *current_record, current_meta, txn4, txn_manager_.get(),
        undo_mgr_.get(), visible_record, visible_meta
    );
    
    EXPECT_TRUE(found);
    EXPECT_FALSE(visible_meta.is_deleted_);
    
    // 验证可见数据是Version 3
    auto visible_user_data = MVCCHelper::GetUserData(visible_record);
    EXPECT_EQ(memcmp(visible_user_data->data, buffer3, strlen(version3)), 0);
    
    txn_manager_->commit(txn4, log_manager_.get());
    delete txn1;
    delete txn2;
    delete txn3;
    delete txn4;
}

// 测试5：删除记录的版本重构
TEST_F(MVCCStage2Test, DeletedRecordReconstruction) {
    // 创建记录
    auto* txn1 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context1 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn1, undo_mgr_.get());
    
    const char* original_data = "Original Data";
    char original_buffer[64];
    memset(original_buffer, 0, 64);
    strncpy(original_buffer, original_data, strlen(original_data));
    
    Rid record_rid = file_handle_->insert_record(original_buffer, context1.get());
    txn_manager_->commit(txn1, log_manager_.get());
    
    // 删除记录
    auto* txn2 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context2 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn2, undo_mgr_.get());
    
    file_handle_->delete_record(record_rid, context2.get());
    txn_manager_->commit(txn2, log_manager_.get());
    
    // 新事务尝试读取已删除的记录
    auto* txn3 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context3 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn3, undo_mgr_.get());
    
    auto current_record = file_handle_->get_record(record_rid, context3.get());
    TupleMeta current_meta = file_handle_->extract_tuple_meta(*current_record);
    
    // 验证记录已删除
    EXPECT_TRUE(current_meta.is_deleted_);
    
    // 测试版本重构 - 应该能找到删除前的版本
    RmRecord visible_record;
    TupleMeta visible_meta;
    
    bool found = MVCCHelper::ReconstructTuple(
        *current_record, current_meta, txn3, txn_manager_.get(),
        undo_mgr_.get(), visible_record, visible_meta
    );
    
    // 根据快照隔离，新事务应该看到删除前的版本
    EXPECT_TRUE(found);
    EXPECT_FALSE(visible_meta.is_deleted_);
    
    // 验证数据是原始数据
    auto visible_user_data = MVCCHelper::GetUserData(visible_record);
    EXPECT_EQ(memcmp(visible_user_data->data, original_buffer, strlen(original_data)), 0);
    
    txn_manager_->commit(txn3, log_manager_.get());
    delete txn1;
    delete txn2;
    delete txn3;
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
} 