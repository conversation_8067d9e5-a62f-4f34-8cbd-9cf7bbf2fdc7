#include "gtest/gtest.h"
#include "parser/ast.h"
#include "parser/parser.h" // For ast::parse_tree and Flex/Bison functions
#include "analyze/analyze.h"
#include "optimizer/optimizer.h" // For Optimizer
#include "optimizer/planner.h"   // For Planner
#include "optimizer/plan.h"      // For Plan and derived classes
#include "portal.h"              // For Portal
#include "execution/executor_abstract.h" // For AbstractExecutor
#include "system/sm_manager.h"
#include "storage/disk_manager.h"
#include "storage/buffer_pool_manager.h"
#include "record/rm_manager.h"
#include "record/rm_defs.h" // For RmRecord
#include "index/ix_manager.h"
#include "errors.h"
#include "defs.h" // For ColType
#include "common/common.h" 
#include "common/context.h"

#include <string>
#include <vector>
#include <memory>
#include <filesystem>
#include <algorithm> 
#include <set>       

// Forward declarations for Flex/Bison functions
extern int yyparse();
typedef struct yy_buffer_state *YY_BUFFER_STATE;
extern YY_BUFFER_STATE yy_scan_string(const char *yystr);
extern void yy_delete_buffer(YY_BUFFER_STATE b);
// extern void parser_init(); 
// extern void parser_destroy();


std::shared_ptr<ast::TreeNode> parse_sql_for_e2e_test(const std::string& sql) {
    ast::parse_tree.reset(); 
    YY_BUFFER_STATE buffer = yy_scan_string((sql + ";").c_str());
    int result = yyparse();
    yy_delete_buffer(buffer);
    if (result != 0) {
        std::cerr << "SQL Parse Error for: " << sql << std::endl;
        return nullptr;
    }
    return ast::parse_tree;
}

std::vector<std::string> record_to_strings(const RmRecord* record, const std::vector<ColMeta>& cols) {
    std::vector<std::string> values;
    if (!record || !record->data) return values;

    for (const auto& col : cols) {
        char* val_ptr = record->data + col.offset;
        switch (col.type) {
            case TYPE_INT:
                values.push_back(std::to_string(*reinterpret_cast<int*>(val_ptr)));
                break;
            case TYPE_FLOAT:
                values.push_back(std::to_string(*reinterpret_cast<float*>(val_ptr)));
                break;
            case TYPE_STRING: {
                std::string s(val_ptr, strnlen(val_ptr, col.len));
                values.push_back(s);
                break;
            }
            default:
                values.push_back("[UNKNOWN_TYPE:" + std::to_string(col.type) + "]");
                break;
        }
    }
    return values;
}


const std::string E2E_TEST_DB_NAME_OJ = "semi_join_oj_test_db";
const int E2E_TEST_BUFFER_POOL_SIZE_OJ = 25; 

class SemiJoinE2ETest : public ::testing::Test {
protected:
    DiskManager* disk_manager_ = nullptr;
    BufferPoolManager* buffer_pool_manager_ = nullptr;
    RmManager* rm_manager_ = nullptr;
    IxManager* ix_manager_ = nullptr;
    SmManager* sm_manager_ = nullptr;
    Analyze* analyzer_ = nullptr;
    Planner* planner_ = nullptr;
    Optimizer* optimizer_ = nullptr;
    Portal* portal_ = nullptr;
    Context* context_ = nullptr; 

    void SetUp() override {
        if (std::filesystem::exists(E2E_TEST_DB_NAME_OJ)) {
            std::filesystem::remove_all(E2E_TEST_DB_NAME_OJ);
        }
        if (std::filesystem::exists(E2E_TEST_DB_NAME_OJ + ".meta")) {
             std::filesystem::remove(E2E_TEST_DB_NAME_OJ + ".meta");
        }

        disk_manager_ = new DiskManager();
        buffer_pool_manager_ = new BufferPoolManager(E2E_TEST_BUFFER_POOL_SIZE_OJ, disk_manager_);
        rm_manager_ = new RmManager(disk_manager_, buffer_pool_manager_);
        ix_manager_ = new IxManager(disk_manager_, buffer_pool_manager_);
        sm_manager_ = new SmManager(disk_manager_, buffer_pool_manager_, rm_manager_, ix_manager_);
        
        context_ = new Context(nullptr, nullptr, nullptr); 

        sm_manager_->create_db(E2E_TEST_DB_NAME_OJ);
        sm_manager_->open_db(E2E_TEST_DB_NAME_OJ);

        // Create tables for OJ tests
        // departments (dept_id INT, dept_name CHAR(20))
        std::vector<ColDef> dept_cols = { {"dept_id", TYPE_INT, sizeof(int)}, {"dept_name", TYPE_STRING, 20} };
        sm_manager_->create_table("departments", dept_cols, context_);

        // employees (emp_id INT, emp_name CHAR(20), dept_id INT)
        std::vector<ColDef> emp_cols = { 
            {"emp_id", TYPE_INT, sizeof(int)}, 
            {"emp_name", TYPE_STRING, 20},
            {"dept_id", TYPE_INT, sizeof(int)} 
        };
        sm_manager_->create_table("employees", emp_cols, context_);
        
        // projects (proj_id INT, dept_id_assigned INT)
        std::vector<ColDef> proj_cols = { {"proj_id", TYPE_INT, sizeof(int)}, {"dept_id_assigned", TYPE_INT, sizeof(int)} };
        sm_manager_->create_table("projects", proj_cols, context_);

        // empty_departments (dept_id INT, dept_name CHAR(20))
        std::vector<ColDef> empty_dept_cols = { {"dept_id", TYPE_INT, sizeof(int)}, {"dept_name", TYPE_STRING, 20} };
        sm_manager_->create_table("empty_departments", empty_dept_cols, context_);


        analyzer_ = new Analyze(sm_manager_);
        planner_ = new Planner(sm_manager_); 
        optimizer_ = new Optimizer(sm_manager_, planner_); 
        portal_ = new Portal(sm_manager_); 

        // Insert data for OJ tests
        execute_sql_no_results("INSERT INTO departments VALUES (1, 'HR');");
        execute_sql_no_results("INSERT INTO departments VALUES (2, 'Engineering');");
        execute_sql_no_results("INSERT INTO departments VALUES (3, 'Sales');");
        execute_sql_no_results("INSERT INTO departments VALUES (4, 'Marketing');");

        execute_sql_no_results("INSERT INTO employees VALUES (101, 'Alice', 1);");
        execute_sql_no_results("INSERT INTO employees VALUES (102, 'Bob', 2);");
        execute_sql_no_results("INSERT INTO employees VALUES (103, 'Charlie', 1);"); // Another employee in HR
        execute_sql_no_results("INSERT INTO employees VALUES (104, 'David', 2);");   // Another employee in Engineering
        execute_sql_no_results("INSERT INTO employees VALUES (105, 'Eve', 5);");     // Employee in a non-existent dept for this test set
    }

    void TearDown() override {
        if (sm_manager_) sm_manager_->close_db();
        
        delete context_;
        delete portal_;
        delete optimizer_;
        delete planner_;
        delete analyzer_;
        delete sm_manager_;
        delete ix_manager_;
        delete rm_manager_;
        delete buffer_pool_manager_;
        delete disk_manager_;

        if (std::filesystem::exists(E2E_TEST_DB_NAME_OJ)) {
            std::filesystem::remove_all(E2E_TEST_DB_NAME_OJ);
        }
        if (std::filesystem::exists(E2E_TEST_DB_NAME_OJ + ".meta")) {
             std::filesystem::remove(E2E_TEST_DB_NAME_OJ + ".meta");
        }
        ast::parse_tree.reset(); 
    }

    std::multiset<std::vector<std::string>> execute_query_and_get_results(const std::string& sql) {
        auto ast_root = parse_sql_for_e2e_test(sql);
        if (!ast_root) return {};
        
        std::shared_ptr<Query> query_analyzed = analyzer_->do_analyze(ast_root);
        if (!query_analyzed) return {};
        
        std::shared_ptr<Plan> plan = optimizer_->plan_query(query_analyzed, context_);
        if (!plan) return {};

        std::shared_ptr<DMLPlan> dml_plan = std::dynamic_pointer_cast<DMLPlan>(plan);
        if (!dml_plan || dml_plan->tag != T_select) {
            if (dml_plan && (dml_plan->tag == T_Insert || dml_plan->tag == T_Delete || dml_plan->tag == T_Update)) {
                std::shared_ptr<PortalStmt> portal_stmt = portal_->start(plan, context_);
                if (portal_stmt && portal_stmt->root) {
                    // For DML, Next() might not be the full execution trigger, 
                    // or it might do one step. beginTuple/is_end loop might be more robust if DMLs are iterative.
                    // However, for simple INSERT/DELETE/UPDATE, one Next() or letting Portal::run handle it is typical.
                    // Since we are bypassing QlManager, we need to ensure execution.
                    // A simple DML executor usually performs its action fully on the first Next() or within beginTuple().
                    portal_stmt->root->beginTuple(); 
                    while(!portal_stmt->root->is_end()) {
                         portal_stmt->root->Next();
                    }
                }
                return {};
            }
            std::cerr << "Expected a SELECT DMLPlan but got something else for: " << sql << std::endl;
            return {};
        }
        
        std::shared_ptr<Plan> query_execution_plan = dml_plan->subplan_;
        std::shared_ptr<PortalStmt> portal_stmt = portal_->start(plan, context_); // Portal::start takes the top-level plan
        
        if (!portal_stmt || !portal_stmt->root) {
            std::cerr << "Failed to create PortalStmt or executor tree for: " << sql << std::endl;
            return {};
        }

        std::unique_ptr<AbstractExecutor> exec_tree_root = std::move(portal_stmt->root);
        std::vector<ColMeta> output_cols_meta = exec_tree_root->cols(); 

        std::multiset<std::vector<std::string>> results;
        exec_tree_root->beginTuple();
        while (!exec_tree_root->is_end()) {
            auto rec = exec_tree_root->Next();
            if (rec) { // If a record is successfully retrieved
                results.insert(record_to_strings(rec.get(), output_cols_meta));
                exec_tree_root->nextTuple(); // Advance the main iterator
            } else {
                // If Next() returns nullptr even when !is_end(), it implies an issue or
                // that the end has effectively been reached.
                // To prevent infinite loop if is_end() doesn't update correctly after Next() returns null:
                break;
            }
        }
        return results;
    }
    
    void execute_sql_no_results(const std::string& sql) {
        auto ast_root = parse_sql_for_e2e_test(sql);
        ASSERT_NE(ast_root, nullptr) << "Parse failed for: " << sql;
        
        std::shared_ptr<Query> query_analyzed = analyzer_->do_analyze(ast_root);
        ASSERT_NE(query_analyzed, nullptr) << "Analyze failed for: " << sql;

        std::shared_ptr<Plan> plan = optimizer_->plan_query(query_analyzed, context_);
        ASSERT_NE(plan, nullptr) << "Plan failed for: " << sql;
        
        std::shared_ptr<PortalStmt> portal_stmt = portal_->start(plan, context_);
        ASSERT_NE(portal_stmt, nullptr) << "Portal start failed for: " << sql;

        if (portal_stmt->root) { 
             portal_stmt->root->beginTuple();
             while(!portal_stmt->root->is_end()) {
                 portal_stmt->root->Next();
             }
        } else if (portal_stmt->tag == PORTAL_MULTI_QUERY || portal_stmt->tag == PORTAL_CMD_UTILITY) {
            // This part is tricky without QlManager. For DDL (like CREATE TABLE in SetUp),
            // SmManager calls are made directly or via Portal->QlManager.
            // Here, we assume DDLs in SetUp are handled by SmManager directly.
            // For INSERTs, the above root->Next() loop should work.
        }
    }
};

// OJ Test Point 1 & 2: Basic Semi Join & Unaffected by right table duplicates
TEST_F(SemiJoinE2ETest, OJ_TestPoint1_2_BasicAndDuplicates) {
    std::string sql = "SELECT dept_id, dept_name FROM departments SEMI JOIN employees ON departments.dept_id = employees.dept_id;";
    auto results = execute_query_and_get_results(sql);
    
    std::multiset<std::vector<std::string>> expected_results = {
        {"1", "HR"},
        {"2", "Engineering"}
    };
    // Use EXPECT_EQ for multiset comparison. Order doesn't matter.
    EXPECT_EQ(results, expected_results);
}

// OJ Test Point 3: Right table empty or no match
TEST_F(SemiJoinE2ETest, OJ_TestPoint3_RightTableEmptyOrNoMatch) {
    // Scenario 1: projects table is initially empty (as per SetUp)
    std::string sql1 = "SELECT dept_name FROM departments SEMI JOIN projects ON departments.dept_id = projects.dept_id_assigned;";
    auto results1 = execute_query_and_get_results(sql1);
    EXPECT_TRUE(results1.empty()) << "Expected empty result when projects table is empty.";

    // Scenario 2: Insert a non-matching project
    execute_sql_no_results("INSERT INTO projects VALUES(1001, 99);");
    std::string sql2 = "SELECT dept_name FROM departments SEMI JOIN projects ON departments.dept_id = projects.dept_id_assigned;";
    auto results2 = execute_query_and_get_results(sql2);
    EXPECT_TRUE(results2.empty()) << "Expected empty result when projects table has no matching dept_id.";
}

// OJ Test Point 4: Robustness - Selecting column from right table
TEST_F(SemiJoinE2ETest, OJ_TestPoint4_SelectFromRightTableFails) {
    std::string sql = "SELECT dept_name, emp_name FROM departments SEMI JOIN employees ON departments.dept_id = employees.dept_id;";
    
    auto ast_root = parse_sql_for_e2e_test(sql);
    ASSERT_NE(ast_root, nullptr);
    // Analyzer should throw an error (e.g., InvalidColumnError or ColumnNotFoundError)
    EXPECT_THROW(analyzer_->do_analyze(ast_root), ColumnNotFoundError) 
        << "Analyzer should prevent selecting columns from the right table in a SEMI JOIN.";
    // Or, if it passes analyzer (which it shouldn't for emp_name), planner or executor might fail.
    // But analyzer is the first gate for this semantic error.
}

// OJ Test Point 5: Robustness - Left table empty
TEST_F(SemiJoinE2ETest, OJ_TestPoint5_LeftTableEmpty) {
    // empty_departments table is created in SetUp and is initially empty.
    std::string sql = "SELECT dept_name FROM empty_departments SEMI JOIN employees ON empty_departments.dept_id = employees.dept_id;";
    auto results = execute_query_and_get_results(sql);
    EXPECT_TRUE(results.empty()) << "Expected empty result when left table (empty_departments) is empty.";
}

// Original tests (modified to remove ORDER BY and adapt if necessary)
TEST_F(SemiJoinE2ETest, Original_BasicMatchNoOrderBy) {
    // Using departments and employees as t1 and t2 from OJ setup
    std::string sql = "SELECT dept_id, dept_name FROM departments SEMI JOIN employees ON departments.dept_id = employees.dept_id;";
    auto results = execute_query_and_get_results(sql);
    
    std::multiset<std::vector<std::string>> expected_results = {
        {"1", "HR"},
        {"2", "Engineering"}
    };
    EXPECT_EQ(results, expected_results);
}

TEST_F(SemiJoinE2ETest, Original_NoMatch) {
    // departments.dept_id (1,2,3,4) vs employees.emp_id (101,102,...)
    std::string sql = "SELECT dept_id FROM departments SEMI JOIN employees ON departments.dept_id = employees.emp_id;"; 
    auto results = execute_query_and_get_results(sql);
    EXPECT_TRUE(results.empty());
}


TEST_F(SemiJoinE2ETest, Original_SelectStarSemiJoinNoOrderBy) {
    std::string sql = "SELECT * FROM departments SEMI JOIN employees ON departments.dept_id = employees.dept_id;";
    auto results = execute_query_and_get_results(sql);
    std::multiset<std::vector<std::string>> expected_results = {
        {"1", "HR"},
        {"2", "Engineering"}
    };
    EXPECT_EQ(results, expected_results);
}

TEST_F(SemiJoinE2ETest, Original_WithWhereClauseOnLeftTable) {
    std::string sql = "SELECT dept_id, dept_name FROM departments SEMI JOIN employees ON departments.dept_id = employees.dept_id WHERE departments.dept_name = 'HR';";
    auto results = execute_query_and_get_results(sql);
    std::multiset<std::vector<std::string>> expected_results = {
        {"1", "HR"}
    };
    EXPECT_EQ(results, expected_results);
}

TEST_F(SemiJoinE2ETest, Original_MultipleOnConditions) {
    // For this, we need a common column or comparable values.
    // Let's make emp_name temporarily match dept_name for specific rows.
    execute_sql_no_results("UPDATE employees SET emp_name = 'HR' WHERE emp_id = 101;"); // dept_id = 1
    execute_sql_no_results("UPDATE employees SET emp_name = 'Sales' WHERE emp_id = 102;"); // dept_id = 2, but emp_name is 'Sales'

    // This query will now only match if dept_id matches AND dept_name matches emp_name
    std::string sql = "SELECT departments.dept_id FROM departments SEMI JOIN employees ON departments.dept_id = employees.dept_id AND departments.dept_name = employees.emp_name;";
    auto results = execute_query_and_get_results(sql);
    std::multiset<std::vector<std::string>> expected_results = {
        {"1"} // Only HR dept (id 1) where dept_name 'HR' matches emp_name 'HR' for emp_id 101
    };
    EXPECT_EQ(results, expected_results);
     // Revert changes if other tests depend on original emp_names
    execute_sql_no_results("UPDATE employees SET emp_name = 'Alice' WHERE emp_id = 101;");
    execute_sql_no_results("UPDATE employees SET emp_name = 'Bob' WHERE emp_id = 102;");
}