# Include CTest module to enable testing with CTest
include(CTest)
add_executable(parser_test parser_test/semi_join_parser_test.cpp)
target_link_libraries(parser_test PRIVATE gtest_main parser)
add_test(NAME ParserSemiJoinTest COMMAND parser_test)

# Analyzer tests
add_executable(analyzer_test analyzer_test/semi_join_analyzer_test.cpp)
target_link_libraries(analyzer_test PRIVATE
    gtest_main
    parser      # For parsing SQL into AST
    analyze     # The library being tested
    system      # For SmManager, DbMeta, etc.
    index       # Dependency of system
    record      # Dependency of system
    storage     # Dependency of index, record
    transaction # For Context, LockManager, Transaction
    recovery    # For Context, LogManager
    # pthread     # Might be needed due to transaction/recovery linking it
)
# Add filesystem for C++17 filesystem features used in test setup/teardown
target_link_libraries(analyzer_test PRIVATE stdc++fs)


add_test(NAME AnalyzerSemiJoinTest COMMAND analyzer_test)

# E2E Query tests for SEMI JOIN
add_executable(e2e_query_test e2e_query_test/semi_join_e2e_test.cpp)
target_link_libraries(e2e_query_test PRIVATE
    gtest_main
    parser      # For parsing SQL into AST
    analyze     # For semantic analysis
    planner     # For Planner (Optimizer uses Planner and is likely header-only or part of other libs)
    execution   # For Portal and executor implementations
    system      # For SmManager, DbMeta, etc.
    index       # Dependency of system/execution
    record      # Dependency of system/execution
    storage     # Dependency of index, record
    transaction # For Context, LockManager, Transaction (used by SmManager, execution context)
    recovery    # For Context, LogManager (used by SmManager, execution context)
    # pthread     # Might be needed due to transaction/recovery linking it
)
# Add filesystem for C++17 filesystem features used in test setup/teardown
target_link_libraries(e2e_query_test PRIVATE stdc++fs)

add_test(NAME E2ESemiJoinTest COMMAND e2e_query_test)

# Test for multi-column index correctness
add_executable(multi_index_test judge_multi_index_test/multi_index_test.cpp)
target_link_libraries(multi_index_test PRIVATE gtest_main)
add_test(NAME MultiIndexCorrectnessTest COMMAND multi_index_test)

# MVCC Unit Tests
add_executable(mvcc_unit_test mvcc_test/mvcc_unit_test.cpp)
target_link_libraries(mvcc_unit_test PRIVATE
    gtest_main
    transaction # For UndoLogManager, MVCCHelper, TransactionManager
    record      # For RmRecord, TupleMeta
    system      # For SmManager dependencies
    storage     # For BufferPoolManager dependencies
    recovery    # For LogManager dependencies
)
add_test(NAME MVCCUnitTest COMMAND mvcc_unit_test)

# MVCC Integration Tests
add_executable(mvcc_integration_test mvcc_test/mvcc_integration_test.cpp)
target_link_libraries(mvcc_integration_test PRIVATE
    gtest_main
    transaction # For UndoLogManager, MVCCHelper, TransactionManager
    record      # For RmRecord, TupleMeta
    system      # For SmManager dependencies
    storage     # For BufferPoolManager dependencies
    recovery    # For LogManager dependencies
)
add_test(NAME MVCCIntegrationTest COMMAND mvcc_integration_test)

# MVCC Stage 1 Test - Record Format Reconstruction
add_executable(mvcc_stage1_test mvcc_stage1_test.cpp)
target_link_libraries(mvcc_stage1_test gtest_main transaction system storage record index recovery ${GMOCK_LIBRARIES} ${CMAKE_THREAD_LIBS_INIT} ${READLINE_LIBRARY})

# MVCC Stage 2 Tests - Transaction Manager & Timestamp
add_executable(mvcc_stage2_test mvcc_stage2_test.cpp)
target_link_libraries(mvcc_stage2_test gtest_main transaction system storage record index recovery ${GMOCK_LIBRARIES} ${CMAKE_THREAD_LIBS_INIT} ${READLINE_LIBRARY})

# MVCC Stage 3 Tests - Executor MVCC Support
add_executable(mvcc_stage3_test mvcc_stage3_test.cpp)
target_link_libraries(mvcc_stage3_test gtest_main transaction system storage record index recovery execution ${GMOCK_LIBRARIES} ${CMAKE_THREAD_LIBS_INIT} ${READLINE_LIBRARY})

# MVCC Stage 4 Test - 写入冲突检测
add_executable(mvcc_stage4_test mvcc_stage4_test.cpp)
target_link_libraries(mvcc_stage4_test gtest_main transaction system storage record index recovery execution ${GMOCK_LIBRARIES} ${CMAKE_THREAD_LIBS_INIT} ${READLINE_LIBRARY})