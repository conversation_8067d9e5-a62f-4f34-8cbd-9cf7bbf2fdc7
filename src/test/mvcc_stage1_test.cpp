/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include <gtest/gtest.h>
#include <memory>
#include <string>

#include "storage/disk_manager.h"
#include "storage/buffer_pool_manager.h"
#include "record/rm_manager.h"
#include "record/rm_file_handle.h"
#include "transaction/transaction_manager.h"
#include "transaction/mvcc_helper.h"
#include "system/sm_manager.h"
#include "common/context.h"

/**
 * @brief MVCC阶段1测试类 - 记录格式重构与TupleMeta集成
 * 
 * 测试内容：
 * 1. 记录格式重构（TupleMeta + 用户数据）
 * 2. TupleMeta读写操作
 * 3. 记录CRUD操作的MVCC支持
 * 4. 用户数据提取
 * 5. 向后兼容性
 */
class MVCCStage1Test : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化存储管理器
        disk_manager_ = std::make_unique<DiskManager>();
        buffer_pool_manager_ = std::make_unique<BufferPoolManager>(100, disk_manager_.get());
        rm_manager_ = std::make_unique<RmManager>(disk_manager_.get(), buffer_pool_manager_.get());
        
        // 初始化事务管理器
        lock_manager_ = std::make_unique<LockManager>();
        log_manager_ = std::make_unique<LogManager>(disk_manager_.get());
        txn_manager_ = std::make_unique<TransactionManager>(lock_manager_.get(), nullptr, ConcurrencyMode::MVCC);
        
        // 创建唯一的测试表文件名（基于测试名称）
        auto test_info = ::testing::UnitTest::GetInstance()->current_test_info();
        test_table_name_ = std::string("test_") + test_info->test_case_name() + "_" + test_info->name();
        user_record_size_ = 64;  // 用户数据大小
        
        // 确保文件不存在，如果存在则先删除
        try {
            rm_manager_->destroy_file(test_table_name_);
        } catch (const std::exception&) {
            // 忽略文件不存在的错误
        }
        
        // 创建测试表文件
        rm_manager_->create_file(test_table_name_, user_record_size_);
        file_handle_ = rm_manager_->open_file(test_table_name_);
    }

    void TearDown() override {
        // 先关闭文件句柄
        if (file_handle_) {
            file_handle_.reset();
        }
        
        // 再删除文件
        try {
            rm_manager_->destroy_file(test_table_name_);
        } catch (const std::exception&) {
            // 忽略文件删除错误
        }
    }

protected:
    std::unique_ptr<DiskManager> disk_manager_;
    std::unique_ptr<BufferPoolManager> buffer_pool_manager_;
    std::unique_ptr<RmManager> rm_manager_;
    std::unique_ptr<LockManager> lock_manager_;
    std::unique_ptr<LogManager> log_manager_;
    std::unique_ptr<TransactionManager> txn_manager_;
    std::unique_ptr<RmFileHandle> file_handle_;
    
    std::string test_table_name_;
    int user_record_size_;
};

// 测试1：记录格式验证 - 确保记录包含TupleMeta + 用户数据
TEST_F(MVCCStage1Test, RecordFormatValidation) {
    // 验证文件头信息是否正确设置
    auto file_hdr = file_handle_->get_file_hdr();
    
    // 实际记录大小应该是 TupleMeta + 用户数据
    int expected_total_size = sizeof(TupleMeta) + user_record_size_;
    EXPECT_EQ(file_hdr.record_size, expected_total_size);
    EXPECT_EQ(file_hdr.user_record_size, user_record_size_);
    
    // 验证TupleMeta大小合理性
    EXPECT_GT(sizeof(TupleMeta), 0);
    EXPECT_LT(sizeof(TupleMeta), 100);  // 应该不会太大
    
    std::cout << "TupleMeta size: " << sizeof(TupleMeta) << " bytes" << std::endl;
    std::cout << "User record size: " << user_record_size_ << " bytes" << std::endl;
    std::cout << "Total record size: " << expected_total_size << " bytes" << std::endl;
}

// 测试2：TupleMeta读写操作
TEST_F(MVCCStage1Test, TupleMetaReadWrite) {
    // 创建测试记录
    int full_record_size = sizeof(TupleMeta) + user_record_size_;
    RmRecord test_record(full_record_size);
    memset(test_record.data, 0, full_record_size);
    
    // 创建测试TupleMeta
    TupleMeta original_meta;
    original_meta.ts_ = 12345;
    original_meta.is_deleted_ = false;
    original_meta.creator_txn_id_ = 999;
    original_meta.prev_version_ptr_ = 888;
    
    // 测试SetTupleMeta
    Rid dummy_rid{1, 1};
    MVCCHelper::SetTupleMeta(test_record, original_meta, dummy_rid);
    
    // 测试ExtractTupleMeta
    TupleMeta extracted_meta = MVCCHelper::ExtractTupleMeta(test_record, dummy_rid);
    
    // 验证读写一致性
    EXPECT_EQ(extracted_meta.ts_, original_meta.ts_);
    EXPECT_EQ(extracted_meta.is_deleted_, original_meta.is_deleted_);
    EXPECT_EQ(extracted_meta.creator_txn_id_, original_meta.creator_txn_id_);
    EXPECT_EQ(extracted_meta.prev_version_ptr_, original_meta.prev_version_ptr_);
}

// 测试3：用户数据提取
TEST_F(MVCCStage1Test, UserDataExtraction) {
    // 创建测试记录
    int full_record_size = sizeof(TupleMeta) + user_record_size_;
    RmRecord test_record(full_record_size);
    
    // 设置TupleMeta部分
    TupleMeta meta;
    meta.ts_ = 100;
    meta.is_deleted_ = false;
    meta.creator_txn_id_ = 42;
    meta.prev_version_ptr_ = INVALID_UNDO_PTR;
    memcpy(test_record.data, &meta, sizeof(TupleMeta));
    
    // 设置用户数据部分
    const char* test_user_data = "Hello, MVCC World!";
    size_t test_data_len = strlen(test_user_data);
    memcpy(test_record.data + sizeof(TupleMeta), test_user_data, test_data_len);
    
    // 提取用户数据
    auto user_data = MVCCHelper::GetUserData(test_record);
    
    // 验证用户数据正确性
    EXPECT_EQ(user_data->size, user_record_size_);
    EXPECT_EQ(memcmp(user_data->data, test_user_data, test_data_len), 0);
}

// 测试4：记录插入操作的MVCC支持
TEST_F(MVCCStage1Test, InsertRecordMVCCSupport) {
    // 创建事务
    auto* txn = txn_manager_->begin(nullptr, log_manager_.get());
    auto context = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn);
    
    // 创建用户数据
    const char* user_data = "Test insert data";
    char user_buffer[64];
    memset(user_buffer, 0, 64);
    strncpy(user_buffer, user_data, strlen(user_data));
    
    // 插入记录
    Rid inserted_rid = file_handle_->insert_record(user_buffer, context.get());
    
    // 获取插入的记录
    auto retrieved_record = file_handle_->get_record(inserted_rid, context.get());
    
    // 验证记录格式：应该包含TupleMeta + 用户数据
    EXPECT_EQ(retrieved_record->size, sizeof(TupleMeta) + user_record_size_);
    
    // 提取并验证TupleMeta
    TupleMeta meta = file_handle_->extract_tuple_meta(*retrieved_record);
    EXPECT_EQ(meta.creator_txn_id_, txn->get_transaction_id());
    EXPECT_EQ(meta.ts_, txn->get_read_ts());
    EXPECT_FALSE(meta.is_deleted_);
    EXPECT_EQ(meta.prev_version_ptr_, INVALID_UNDO_PTR);
    
    // 提取并验证用户数据
    auto extracted_user_data = file_handle_->get_user_data(*retrieved_record);
    EXPECT_EQ(extracted_user_data->size, user_record_size_);
    EXPECT_EQ(memcmp(extracted_user_data->data, user_buffer, strlen(user_data)), 0);
    
    // 清理
    txn_manager_->commit(txn, log_manager_.get());
    delete txn;
}

// 测试5：记录更新操作的MVCC支持
TEST_F(MVCCStage1Test, UpdateRecordMVCCSupport) {
    // 创建第一个事务插入记录
    auto* txn1 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context1 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn1);
    
    const char* original_data = "Original data";
    char original_buffer[64];
    memset(original_buffer, 0, 64);
    strncpy(original_buffer, original_data, strlen(original_data));
    
    Rid record_rid = file_handle_->insert_record(original_buffer, context1.get());
    txn_manager_->commit(txn1, log_manager_.get());
    
    // 创建第二个事务更新记录
    auto* txn2 = txn_manager_->begin(nullptr, log_manager_.get());
    auto context2 = std::make_unique<Context>(lock_manager_.get(), log_manager_.get(), txn2);
    
    const char* updated_data = "Updated data";
    char updated_buffer[64];
    memset(updated_buffer, 0, 64);
    strncpy(updated_buffer, updated_data, strlen(updated_data));
    
    // 更新记录
    file_handle_->update_record(record_rid, updated_buffer, context2.get());
    
    // 获取更新后的记录
    auto updated_record = file_handle_->get_record(record_rid, context2.get());
    
    // 验证TupleMeta已更新
    TupleMeta updated_meta = file_handle_->extract_tuple_meta(*updated_record);
    EXPECT_EQ(updated_meta.creator_txn_id_, txn2->get_transaction_id());
    EXPECT_EQ(updated_meta.ts_, txn2->get_read_ts());
    EXPECT_FALSE(updated_meta.is_deleted_);
    
    // 验证用户数据已更新
    auto extracted_user_data = file_handle_->get_user_data(*updated_record);
    EXPECT_EQ(memcmp(extracted_user_data->data, updated_buffer, strlen(updated_data)), 0);
    
    // 清理
    txn_manager_->commit(txn2, log_manager_.get());
    delete txn1;
    delete txn2;
}

// 测试6：向后兼容性 - 处理小记录
TEST_F(MVCCStage1Test, BackwardCompatibilitySmallRecords) {
    // 创建小于TupleMeta大小的记录
    int small_size = sizeof(TupleMeta) / 2;
    RmRecord small_record(small_size);
    memset(small_record.data, 0x42, small_size);  // 填充测试数据
    
    Rid test_rid{1, 1};
    
    // 提取TupleMeta应该返回默认值
    TupleMeta default_meta = MVCCHelper::ExtractTupleMeta(small_record, test_rid);
    EXPECT_EQ(default_meta.creator_txn_id_, INVALID_TXN_ID);
    EXPECT_EQ(default_meta.prev_version_ptr_, INVALID_UNDO_PTR);
    EXPECT_EQ(default_meta.ts_, 0);
    EXPECT_FALSE(default_meta.is_deleted_);
    
    // 提取用户数据应该返回空记录
    auto user_data = MVCCHelper::GetUserData(small_record);
    EXPECT_EQ(user_data->size, 0);
}

// 测试7：完整记录创建和操作
TEST_F(MVCCStage1Test, FullRecordCreationAndOperations) {
    // 创建事务
    auto* txn = txn_manager_->begin(nullptr, log_manager_.get());
    
    // 创建TupleMeta
    TupleMeta meta;
    meta.ts_ = 54321;
    meta.is_deleted_ = false;
    meta.creator_txn_id_ = txn->get_transaction_id();
    meta.prev_version_ptr_ = 999;
    
    // 创建用户数据
    const char* user_data = "Test data for full record";
    char user_buffer[64];
    memset(user_buffer, 0, 64);
    strncpy(user_buffer, user_data, strlen(user_data));
    
    // 使用RmFileHandle的create_full_record方法
    auto full_record = file_handle_->create_full_record(user_buffer, meta);
    
    // 验证完整记录
    EXPECT_EQ(full_record->size, sizeof(TupleMeta) + user_record_size_);
    
    // 验证TupleMeta部分
    TupleMeta extracted_meta = file_handle_->extract_tuple_meta(*full_record);
    EXPECT_EQ(extracted_meta.ts_, meta.ts_);
    EXPECT_EQ(extracted_meta.creator_txn_id_, meta.creator_txn_id_);
    EXPECT_EQ(extracted_meta.prev_version_ptr_, meta.prev_version_ptr_);
    EXPECT_EQ(extracted_meta.is_deleted_, meta.is_deleted_);
    
    // 验证用户数据部分
    auto extracted_user_data = file_handle_->get_user_data(*full_record);
    EXPECT_EQ(memcmp(extracted_user_data->data, user_buffer, strlen(user_data)), 0);
    
    // 清理
    txn_manager_->abort(txn, log_manager_.get());
    delete txn;
}

// 测试8：边界条件测试
TEST_F(MVCCStage1Test, BoundaryConditions) {
    // 测试空用户数据
    char empty_buffer[64];
    memset(empty_buffer, 0, 64);
    
    TupleMeta meta;
    auto full_record = file_handle_->create_full_record(empty_buffer, meta);
    
    auto extracted_user_data = file_handle_->get_user_data(*full_record);
    EXPECT_EQ(extracted_user_data->size, user_record_size_);
    
    // 验证全零数据
    bool all_zero = true;
    for (int i = 0; i < extracted_user_data->size; i++) {
        if (extracted_user_data->data[i] != 0) {
            all_zero = false;
            break;
        }
    }
    EXPECT_TRUE(all_zero);
    
    // 测试最大用户数据
    char max_buffer[64];
    memset(max_buffer, 0xFF, 64);
    
    auto full_record2 = file_handle_->create_full_record(max_buffer, meta);
    auto extracted_user_data2 = file_handle_->get_user_data(*full_record2);
    
    EXPECT_EQ(memcmp(extracted_user_data2->data, max_buffer, user_record_size_), 0);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
} 